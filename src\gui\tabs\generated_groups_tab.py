"""
Generated Groups tab for the TeleGroup Automator GUI.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from typing import Callable, List
import logging
from datetime import datetime

try:
    import pyperclip
    CLIPBOARD_AVAILABLE = True
except ImportError:
    CLIPBOARD_AVAILABLE = False

from database.models import DatabaseManager, SuperGroup
from utils.clipboard_support import ClipboardMixin

class GeneratedGroupsTab(ClipboardMixin):
    """GUI tab for displaying and managing generated group links."""

    def __init__(self, parent, db_manager: DatabaseManager,
                 status_callback: Callable[[str], None]):
        super().__init__()  # Initialize ClipboardMixin
        self.parent = parent
        self.db_manager = db_manager
        self.status_callback = status_callback
        self.logger = logging.getLogger("GeneratedGroupsTab")
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        self.setup_ui()
        self.refresh_groups()
    
    def setup_ui(self):
        """Set up the user interface for the generated groups tab."""
        # Configure grid weights
        self.frame.grid_rowconfigure(2, weight=1)
        self.frame.grid_columnconfigure(0, weight=1)
        
        # Title
        title_label = ttk.Label(self.frame, text="Generated Groups", 
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, sticky="w", padx=10, pady=10)
        
        # Controls frame
        controls_frame = ttk.Frame(self.frame)
        controls_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=(0, 10))
        controls_frame.grid_columnconfigure(2, weight=1)
        
        # Filter controls
        ttk.Label(controls_frame, text="Filter by Account:").grid(row=0, column=0, sticky="w")
        
        self.account_filter_var = tk.StringVar(value="All Accounts")
        self.account_filter_combo = ttk.Combobox(
            controls_frame,
            textvariable=self.account_filter_var,
            state="readonly",
            width=20
        )
        self.account_filter_combo.grid(row=0, column=1, sticky="w", padx=(5, 10))
        self.account_filter_combo.bind("<<ComboboxSelected>>", self.filter_groups)
        
        # Action buttons
        button_frame = ttk.Frame(controls_frame)
        button_frame.grid(row=0, column=3, sticky="e")
        
        self.refresh_btn = ttk.Button(
            button_frame,
            text="Refresh",
            command=self.refresh_groups
        )
        self.refresh_btn.pack(side="left", padx=(0, 5))
        
        self.copy_all_btn = ttk.Button(
            button_frame,
            text="Copy All Links",
            command=self.copy_all_links
        )
        self.copy_all_btn.pack(side="left", padx=(0, 5))
        
        self.export_btn = ttk.Button(
            button_frame,
            text="Export to File",
            command=self.export_to_file
        )
        self.export_btn.pack(side="left")
        
        # Groups display frame
        display_frame = ttk.LabelFrame(self.frame, text="Group Links")
        display_frame.grid(row=2, column=0, sticky="nsew", padx=10, pady=(0, 10))
        display_frame.grid_rowconfigure(0, weight=1)
        display_frame.grid_columnconfigure(0, weight=1)
        
        # Create treeview for groups
        columns = ("Group Name", "Account", "Invite Link", "Created", "Messages")
        self.groups_tree = ttk.Treeview(display_frame, columns=columns, show="headings", height=15)
        
        # Configure columns
        self.groups_tree.heading("Group Name", text="Group Name")
        self.groups_tree.heading("Account", text="Account")
        self.groups_tree.heading("Invite Link", text="Invite Link")
        self.groups_tree.heading("Created", text="Created")
        self.groups_tree.heading("Messages", text="Messages")
        
        self.groups_tree.column("Group Name", width=200)
        self.groups_tree.column("Account", width=150)
        self.groups_tree.column("Invite Link", width=300)
        self.groups_tree.column("Created", width=120)
        self.groups_tree.column("Messages", width=80)
        
        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(display_frame, orient="vertical", command=self.groups_tree.yview)
        h_scrollbar = ttk.Scrollbar(display_frame, orient="horizontal", command=self.groups_tree.xview)
        self.groups_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid the treeview and scrollbars
        self.groups_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # Context menu
        self.context_menu = tk.Menu(self.frame, tearoff=0)
        self.context_menu.add_command(label="Copy Link", command=self.copy_selected_link)
        self.context_menu.add_command(label="Copy Group Name", command=self.copy_selected_name)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Open in Browser", command=self.open_selected_link)
        
        # Bind events
        self.groups_tree.bind("<Button-3>", self.show_context_menu)
        self.groups_tree.bind("<Double-1>", self.copy_selected_link)
        
        # Statistics frame
        stats_frame = ttk.LabelFrame(self.frame, text="Statistics")
        stats_frame.grid(row=3, column=0, sticky="ew", padx=10, pady=(0, 10))
        
        self.stats_label = ttk.Label(stats_frame, text="Loading statistics...")
        self.stats_label.grid(row=0, column=0, sticky="w", padx=10, pady=10)
        
        # Instructions frame
        instructions_frame = ttk.LabelFrame(self.frame, text="Instructions")
        instructions_frame.grid(row=4, column=0, sticky="ew", padx=10, pady=(0, 10))
        
        instructions_text = (
            "• Double-click on a group to copy its invite link\n"
            "• Right-click for more options\n"
            "• Use 'Copy All Links' to copy all visible links to clipboard\n"
            "• Filter by account to view groups from specific accounts\n"
            "• Export to file to save group information"
        )
        
        instructions_label = ttk.Label(instructions_frame, text=instructions_text, justify="left")
        instructions_label.grid(row=0, column=0, sticky="w", padx=10, pady=10)
    
    def refresh_groups(self):
        """Refresh the groups list and update filters."""
        # Get all groups from database
        all_groups = self.db_manager.get_supergroups()
        
        # Update account filter options
        accounts = list(set(group.account_phone for group in all_groups))
        accounts.sort()
        filter_options = ["All Accounts"] + accounts
        
        self.account_filter_combo['values'] = filter_options
        
        # If current selection is not in the list, reset to "All Accounts"
        if self.account_filter_var.get() not in filter_options:
            self.account_filter_var.set("All Accounts")
        
        # Update the display
        self.filter_groups()
        self.update_statistics(all_groups)
        
        self.status_callback(f"Refreshed groups list - {len(all_groups)} total groups found")
    
    def filter_groups(self, event=None):
        """Filter and display groups based on selected account."""
        # Clear existing items
        for item in self.groups_tree.get_children():
            self.groups_tree.delete(item)
        
        # Get filtered groups
        account_filter = self.account_filter_var.get()
        
        if account_filter == "All Accounts":
            groups = self.db_manager.get_supergroups()
        else:
            groups = self.db_manager.get_supergroups(account_filter)
        
        # Sort groups by creation date (newest first)
        groups.sort(key=lambda g: g.created_at, reverse=True)
        
        # Add groups to tree
        for group in groups:
            created_date = group.created_at.strftime("%Y-%m-%d %H:%M")
            
            self.groups_tree.insert("", "end", values=(
                group.group_name,
                group.account_phone,
                group.invite_link,
                created_date,
                group.message_count
            ))
        
        # Update statistics for filtered view
        self.update_statistics(groups)
    
    def update_statistics(self, groups: List[SuperGroup]):
        """Update the statistics display."""
        if not groups:
            self.stats_label.config(text="No groups found")
            return
        
        total_groups = len(groups)
        total_messages = sum(group.message_count for group in groups)
        
        # Count groups by account
        account_counts = {}
        for group in groups:
            account_counts[group.account_phone] = account_counts.get(group.account_phone, 0) + 1
        
        # Find most recent group
        most_recent = max(groups, key=lambda g: g.created_at)
        most_recent_date = most_recent.created_at.strftime("%Y-%m-%d %H:%M")
        
        stats_text = (
            f"Total Groups: {total_groups} | "
            f"Total Messages Sent: {total_messages} | "
            f"Accounts: {len(account_counts)} | "
            f"Most Recent: {most_recent_date}"
        )
        
        self.stats_label.config(text=stats_text)
    
    def show_context_menu(self, event):
        """Show context menu for selected group."""
        item = self.groups_tree.selection()[0] if self.groups_tree.selection() else None
        if item:
            self.context_menu.post(event.x_root, event.y_root)
    
    def copy_selected_link(self, event=None):
        """Copy the selected group's invite link to clipboard."""
        selected = self.groups_tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a group.")
            return
        
        item = selected[0]
        invite_link = self.groups_tree.item(item)["values"][2]
        
        if CLIPBOARD_AVAILABLE:
            try:
                pyperclip.copy(invite_link)
                self.status_callback("Invite link copied to clipboard")
            except Exception as e:
                self.show_text_dialog("Invite Link", invite_link)
        else:
            self.show_text_dialog("Invite Link", invite_link)
    
    def copy_selected_name(self):
        """Copy the selected group's name to clipboard."""
        selected = self.groups_tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a group.")
            return
        
        item = selected[0]
        group_name = self.groups_tree.item(item)["values"][0]
        
        if CLIPBOARD_AVAILABLE:
            try:
                pyperclip.copy(group_name)
                self.status_callback("Group name copied to clipboard")
            except Exception as e:
                self.show_text_dialog("Group Name", group_name)
        else:
            self.show_text_dialog("Group Name", group_name)
    
    def open_selected_link(self):
        """Open the selected group's invite link in browser."""
        selected = self.groups_tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a group.")
            return
        
        item = selected[0]
        invite_link = self.groups_tree.item(item)["values"][2]
        
        try:
            import webbrowser
            webbrowser.open(invite_link)
            self.status_callback("Opened invite link in browser")
        except Exception as e:
            self.logger.error(f"Error opening link: {e}")
            messagebox.showerror("Error", f"Failed to open link: {e}")
    
    def copy_all_links(self):
        """Copy all visible group links to clipboard."""
        # Get all visible items
        items = self.groups_tree.get_children()
        
        if not items:
            messagebox.showinfo("Info", "No groups to copy.")
            return
        
        # Collect all links
        links = []
        for item in items:
            values = self.groups_tree.item(item)["values"]
            group_name = values[0]
            invite_link = values[2]
            links.append(f"{group_name}: {invite_link}")
        
        # Join with newlines
        all_links_text = "\n".join(links)
        
        if CLIPBOARD_AVAILABLE:
            try:
                pyperclip.copy(all_links_text)
                self.status_callback(f"Copied {len(links)} group links to clipboard")
                messagebox.showinfo("Success", f"Copied {len(links)} group links to clipboard!")
            except Exception as e:
                self.show_text_dialog("All Group Links", all_links_text)
        else:
            self.show_text_dialog("All Group Links", all_links_text)
    
    def export_to_file(self):
        """Export group information to a text file."""
        from tkinter import filedialog
        
        # Get all visible items
        items = self.groups_tree.get_children()
        
        if not items:
            messagebox.showinfo("Info", "No groups to export.")
            return
        
        # Ask user for file location
        filename = filedialog.asksaveasfilename(
            title="Export Groups",
            defaultextension=".txt",
            filetypes=[
                ("Text files", "*.txt"),
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]
        )
        
        if not filename:
            return
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                # Write header
                f.write("TeleGroup Automator - Generated Groups Export\n")
                f.write(f"Exported on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Total Groups: {len(items)}\n")
                f.write("-" * 80 + "\n\n")
                
                # Write group information
                for item in items:
                    values = self.groups_tree.item(item)["values"]
                    f.write(f"Group Name: {values[0]}\n")
                    f.write(f"Account: {values[1]}\n")
                    f.write(f"Invite Link: {values[2]}\n")
                    f.write(f"Created: {values[3]}\n")
                    f.write(f"Messages Sent: {values[4]}\n")
                    f.write("-" * 40 + "\n")
            
            self.status_callback(f"Exported {len(items)} groups to {filename}")
            messagebox.showinfo("Success", f"Exported {len(items)} groups to file successfully!")
            
        except Exception as e:
            self.logger.error(f"Error exporting to file: {e}")
            messagebox.showerror("Error", f"Failed to export to file: {e}")
    
    def show_text_dialog(self, title: str, content: str):
        """Show a dialog with text content (fallback for clipboard operations)."""
        dialog = tk.Toplevel(self.frame)
        dialog.title(title)
        dialog.geometry("600x400")
        dialog.grab_set()
        
        # Text widget with scrollbar
        text_frame = ttk.Frame(dialog)
        text_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        text_widget = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD)
        text_widget.pack(fill="both", expand=True)
        text_widget.insert("1.0", content)

        # Add clipboard support to text widget
        self.add_clipboard_support(text_widget)

        text_widget.config(state=tk.DISABLED)

        # Select all text
        text_widget.config(state=tk.NORMAL)
        text_widget.select_range("1.0", tk.END)
        text_widget.config(state=tk.DISABLED)
        
        # Close button
        ttk.Button(
            dialog,
            text="Close",
            command=dialog.destroy
        ).pack(pady=10)
