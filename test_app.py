"""
Test script for the TeleGroup Automator application.
"""

import sys
import os
import logging
import traceback

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    
    try:
        # Test database models
        from database.models import DatabaseManager, TelegramAccount, SuperGroup
        print("✓ Database models imported successfully")
        
        # Test core functionality
        from core.telegram_manager import TelegramAccountManager, TelegramMultiAccountManager
        print("✓ Telegram managers imported successfully")
        
        # Test utilities
        from utils.config import APP_NAME, GROUP_NAME_FORMATS, PREDEFINED_MESSAGES
        from utils.logger import GUILogHandler
        from utils.error_handler import TelegramErrorHandler
        print("✓ Utilities imported successfully")
        
        # Test GUI components
        from gui.main_window import TeleGroupAutomatorGUI
        from gui.tabs.accounts_tab import AccountsTab
        from gui.tabs.create_groups_tab import CreateGroupsTab
        from gui.tabs.bulk_messenger_tab import BulkMessengerTab
        from gui.tabs.generated_groups_tab import GeneratedGroupsTab
        print("✓ GUI components imported successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Import error: {e}")
        traceback.print_exc()
        return False

def test_database():
    """Test database functionality."""
    print("\nTesting database...")
    
    try:
        from database.models import DatabaseManager, TelegramAccount
        from datetime import datetime
        
        # Create test database (clean up first)
        import os
        if os.path.exists("test_data/test.db"):
            os.remove("test_data/test.db")

        db_manager = DatabaseManager("test_data/test.db")
        
        # Test account creation
        test_account = TelegramAccount(
            phone_number="+**********",
            api_id=12345,
            api_hash="test_hash",
            session_file="test_session.session",
            status="Test",
            created_at=datetime.now()
        )
        
        # Test database operations
        db_manager.add_account(test_account)
        print("✓ Account added to database")
        
        accounts = db_manager.get_accounts()
        retrieved_account = next((acc for acc in accounts if acc.phone_number == "+**********"), None)
        if retrieved_account and retrieved_account.phone_number == "+**********":
            print("✓ Account retrieved from database")
        else:
            print("✗ Account retrieval failed")
            return False
        
        # Clean up test database
        import os
        if os.path.exists("test_data/test.db"):
            os.remove("test_data/test.db")
        if os.path.exists("test_data") and not os.listdir("test_data"):
            os.rmdir("test_data")
        
        print("✓ Database test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Database error: {e}")
        traceback.print_exc()
        return False

def test_config():
    """Test configuration values."""
    print("\nTesting configuration...")
    
    try:
        from utils.config import (
            APP_NAME, APP_VERSION, GROUP_NAME_FORMATS, 
            PREDEFINED_MESSAGES, WINDOW_WIDTH, WINDOW_HEIGHT
        )
        
        # Check required config values
        assert APP_NAME, "APP_NAME is empty"
        assert APP_VERSION, "APP_VERSION is empty"
        assert GROUP_NAME_FORMATS, "GROUP_NAME_FORMATS is empty"
        assert PREDEFINED_MESSAGES, "PREDEFINED_MESSAGES is empty"
        assert WINDOW_WIDTH > 0, "WINDOW_WIDTH is invalid"
        assert WINDOW_HEIGHT > 0, "WINDOW_HEIGHT is invalid"
        
        print(f"✓ App Name: {APP_NAME}")
        print(f"✓ App Version: {APP_VERSION}")
        print(f"✓ Group Name Formats: {len(GROUP_NAME_FORMATS)} formats")
        print(f"✓ Predefined Messages: {len(PREDEFINED_MESSAGES)} messages")
        print(f"✓ Window Size: {WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        traceback.print_exc()
        return False

def test_gui_creation():
    """Test GUI creation without showing it."""
    print("\nTesting GUI creation...")
    
    try:
        import tkinter as tk
        from gui.main_window import TeleGroupAutomatorGUI
        
        # Create a test root window
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # Try to create the GUI
        app = TeleGroupAutomatorGUI()
        app.root.withdraw()  # Hide the main window too
        
        # Check that components were created
        assert hasattr(app, 'db_manager'), "Database manager not created"
        assert hasattr(app, 'telegram_manager'), "Telegram manager not created"
        assert hasattr(app, 'accounts_tab'), "Accounts tab not created"
        assert hasattr(app, 'create_groups_tab'), "Create groups tab not created"
        assert hasattr(app, 'bulk_messenger_tab'), "Bulk messenger tab not created"
        assert hasattr(app, 'generated_groups_tab'), "Generated groups tab not created"
        
        print("✓ GUI components created successfully")
        
        # Clean up
        app.root.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI creation error: {e}")
        traceback.print_exc()
        return False

def test_error_handling():
    """Test error handling functionality."""
    print("\nTesting error handling...")
    
    try:
        from utils.error_handler import TelegramErrorHandler
        from telethon import errors
        
        # Create error handler
        error_handler = TelegramErrorHandler()
        
        # Test flood wait handling
        # Create a mock flood wait error (FloodWaitError constructor varies by version)
        try:
            flood_error = errors.FloodWaitError(30)  # Try simple constructor
        except:
            # If that fails, create a mock error with the seconds attribute
            class MockFloodWaitError(Exception):
                def __init__(self, seconds):
                    self.seconds = seconds
            flood_error = MockFloodWaitError(30)

        result = error_handler.handle_telegram_error(flood_error, "+**********")
        
        print("✓ Flood wait error handled")
        
        # Test generic error handling
        generic_error = ValueError("Test error")
        result = error_handler.handle_telegram_error(generic_error, "+**********")
        
        print("✓ Generic error handled")
        
        return True
        
    except Exception as e:
        print(f"✗ Error handling test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=" * 50)
    print("TeleGroup Automator - Application Tests")
    print("=" * 50)
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Create data directory if it doesn't exist
    os.makedirs("data", exist_ok=True)
    os.makedirs("sessions", exist_ok=True)
    
    tests = [
        test_imports,
        test_config,
        test_database,
        test_error_handling,
        test_gui_creation,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")
    print("=" * 50)
    
    if failed == 0:
        print("🎉 All tests passed! The application should work correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
