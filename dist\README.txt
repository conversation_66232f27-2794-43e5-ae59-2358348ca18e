# TeleGroup Automator

## Description
TeleGroup Automator is a desktop application for creating and managing Telegram supergroups across multiple accounts.

## Features
- Multi-account Telegram management
- Automated supergroup creation with configurable naming
- Bulk messaging to created groups
- Professional GUI with tabbed interface
- Comprehensive error handling and logging
- Export functionality for group links

## Usage
1. Run TeleGroupAutomator.exe
2. Add your Telegram accounts in the "Accounts" tab
3. Configure group creation settings in the "Create Groups" tab
4. Use the "Bulk Messenger" tab to send messages to all groups
5. View and manage created groups in the "Generated Groups" tab

## Requirements
- Windows 10 or later
- Internet connection for Telegram API access

## Important Notes
- Keep your API credentials secure
- Respect Telegram's rate limits and terms of service
- The application creates local database and session files

## Support
For issues or questions, please refer to the application logs in the data directory.

## Version
1.0.0

## License
This software is provided as-is for educational and personal use.