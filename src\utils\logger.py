"""
Logging utilities for the TeleGroup Automator application.
"""

import logging
import os
from datetime import datetime
from pathlib import Path

def setup_logger(name="TeleGroupAutomator", level=logging.INFO):
    """
    Set up a logger with both file and console handlers.
    
    Args:
        name (str): Logger name
        level: Logging level
        
    Returns:
        logging.Logger: Configured logger instance
    """
    # Create logs directory if it doesn't exist
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Avoid adding multiple handlers if logger already exists
    if logger.handlers:
        return logger
    
    # Create formatters
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_formatter = logging.Formatter(
        '%(levelname)s - %(message)s'
    )
    
    # File handler
    log_filename = f"telegroup_automator_{datetime.now().strftime('%Y%m%d')}.log"
    file_handler = logging.FileHandler(logs_dir / log_filename, encoding='utf-8')
    file_handler.setLevel(level)
    file_handler.setFormatter(file_formatter)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(console_formatter)
    
    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

class GUILogHandler(logging.Handler):
    """Custom log handler that can send logs to a GUI text widget."""
    
    def __init__(self, text_widget=None):
        super().__init__()
        self.text_widget = text_widget
        
    def set_text_widget(self, text_widget):
        """Set the text widget to display logs in."""
        self.text_widget = text_widget
        
    def emit(self, record):
        """Emit a log record to the GUI text widget."""
        if self.text_widget:
            try:
                msg = self.format(record)
                # Schedule the GUI update in the main thread
                self.text_widget.after(0, self._append_to_widget, msg)
            except Exception:
                pass  # Ignore errors in GUI logging
                
    def _append_to_widget(self, message):
        """Append message to the text widget (must be called from main thread)."""
        if self.text_widget:
            self.text_widget.insert('end', f"{message}\n")
            self.text_widget.see('end')  # Auto-scroll to bottom
