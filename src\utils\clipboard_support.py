"""
Clipboard support utilities for enhancing text input fields with copy/paste functionality.
"""

import tkinter as tk
from tkinter import ttk
import logging


class ClipboardMixin:
    """Mixin class to add comprehensive clipboard support to text widgets."""
    
    def __init__(self):
        self.logger = logging.getLogger("ClipboardSupport")
    
    def add_clipboard_support(self, widget):
        """Add comprehensive clipboard support to a text widget."""
        try:
            # Bind keyboard shortcuts
            widget.bind('<Control-c>', lambda e: self._copy_text(widget))
            widget.bind('<Control-x>', lambda e: self._cut_text(widget))
            widget.bind('<Control-v>', lambda e: self._paste_text(widget))
            widget.bind('<Control-a>', lambda e: self._select_all(widget))
            
            # Bind right-click context menu
            widget.bind('<Button-3>', lambda e: self._show_context_menu(widget, e))
            
            # Store reference for context menu
            widget._clipboard_menu = None
            
        except Exception as e:
            self.logger.error(f"Failed to add clipboard support: {e}")
    
    def _copy_text(self, widget):
        """<PERSON><PERSON> selected text to clipboard."""
        try:
            if hasattr(widget, 'selection_get'):
                # For Entry widgets
                if widget.selection_present():
                    text = widget.selection_get()
                    widget.clipboard_clear()
                    widget.clipboard_append(text)
            elif hasattr(widget, 'get'):
                # For Text widgets
                if widget.tag_ranges(tk.SEL):
                    text = widget.get(tk.SEL_FIRST, tk.SEL_LAST)
                    widget.clipboard_clear()
                    widget.clipboard_append(text)
        except tk.TclError:
            # No selection
            pass
        except Exception as e:
            self.logger.error(f"Copy operation failed: {e}")
        return 'break'
    
    def _cut_text(self, widget):
        """Cut selected text to clipboard."""
        try:
            if hasattr(widget, 'selection_get'):
                # For Entry widgets
                if widget.selection_present():
                    text = widget.selection_get()
                    widget.clipboard_clear()
                    widget.clipboard_append(text)
                    widget.delete(tk.SEL_FIRST, tk.SEL_LAST)
            elif hasattr(widget, 'get'):
                # For Text widgets
                if widget.tag_ranges(tk.SEL):
                    text = widget.get(tk.SEL_FIRST, tk.SEL_LAST)
                    widget.clipboard_clear()
                    widget.clipboard_append(text)
                    widget.delete(tk.SEL_FIRST, tk.SEL_LAST)
        except tk.TclError:
            # No selection
            pass
        except Exception as e:
            self.logger.error(f"Cut operation failed: {e}")
        return 'break'
    
    def _paste_text(self, widget):
        """Paste text from clipboard."""
        try:
            text = widget.clipboard_get()
            if hasattr(widget, 'insert'):
                if hasattr(widget, 'selection_present') and widget.selection_present():
                    # Replace selection in Entry widget
                    widget.delete(tk.SEL_FIRST, tk.SEL_LAST)
                    widget.insert(tk.INSERT, text)
                elif hasattr(widget, 'tag_ranges') and widget.tag_ranges(tk.SEL):
                    # Replace selection in Text widget
                    widget.delete(tk.SEL_FIRST, tk.SEL_LAST)
                    widget.insert(tk.INSERT, text)
                else:
                    # Insert at cursor position
                    widget.insert(tk.INSERT, text)
        except tk.TclError:
            # No clipboard content
            pass
        except Exception as e:
            self.logger.error(f"Paste operation failed: {e}")
        return 'break'
    
    def _select_all(self, widget):
        """Select all text in the widget."""
        try:
            if hasattr(widget, 'select_range'):
                # For Entry widgets
                widget.select_range(0, tk.END)
                widget.icursor(tk.END)
            elif hasattr(widget, 'tag_add'):
                # For Text widgets
                widget.tag_add(tk.SEL, "1.0", tk.END)
                widget.mark_set(tk.INSERT, "1.0")
                widget.see(tk.INSERT)
        except Exception as e:
            self.logger.error(f"Select all operation failed: {e}")
        return 'break'
    
    def _show_context_menu(self, widget, event):
        """Show right-click context menu."""
        try:
            # Create context menu if it doesn't exist
            if not hasattr(widget, '_clipboard_menu') or widget._clipboard_menu is None:
                widget._clipboard_menu = tk.Menu(widget, tearoff=0)
                
                # Add menu items
                widget._clipboard_menu.add_command(
                    label="Cut", 
                    command=lambda: self._cut_text(widget),
                    accelerator="Ctrl+X"
                )
                widget._clipboard_menu.add_command(
                    label="Copy", 
                    command=lambda: self._copy_text(widget),
                    accelerator="Ctrl+C"
                )
                widget._clipboard_menu.add_command(
                    label="Paste", 
                    command=lambda: self._paste_text(widget),
                    accelerator="Ctrl+V"
                )
                widget._clipboard_menu.add_separator()
                widget._clipboard_menu.add_command(
                    label="Select All", 
                    command=lambda: self._select_all(widget),
                    accelerator="Ctrl+A"
                )
            
            # Update menu state based on current selection and clipboard
            self._update_menu_state(widget)
            
            # Show menu at cursor position
            widget._clipboard_menu.tk_popup(event.x_root, event.y_root)
            
        except Exception as e:
            self.logger.error(f"Context menu failed: {e}")
        finally:
            # Ensure menu is properly closed
            if hasattr(widget, '_clipboard_menu') and widget._clipboard_menu:
                widget._clipboard_menu.grab_release()
    
    def _update_menu_state(self, widget):
        """Update context menu item states based on current conditions."""
        try:
            menu = widget._clipboard_menu
            if not menu:
                return
            
            # Check if there's a selection
            has_selection = False
            if hasattr(widget, 'selection_present'):
                has_selection = widget.selection_present()
            elif hasattr(widget, 'tag_ranges'):
                has_selection = bool(widget.tag_ranges(tk.SEL))
            
            # Check if clipboard has content
            has_clipboard = False
            try:
                widget.clipboard_get()
                has_clipboard = True
            except tk.TclError:
                pass
            
            # Update menu states
            menu.entryconfig("Cut", state="normal" if has_selection else "disabled")
            menu.entryconfig("Copy", state="normal" if has_selection else "disabled")
            menu.entryconfig("Paste", state="normal" if has_clipboard else "disabled")
            
        except Exception as e:
            self.logger.error(f"Menu state update failed: {e}")


def enhance_entry_with_clipboard(entry_widget):
    """Standalone function to enhance a single Entry widget with clipboard support."""
    clipboard_helper = ClipboardMixin()
    clipboard_helper.add_clipboard_support(entry_widget)


def enhance_text_with_clipboard(text_widget):
    """Standalone function to enhance a single Text widget with clipboard support."""
    clipboard_helper = ClipboardMixin()
    clipboard_helper.add_clipboard_support(text_widget)


def enhance_all_entries_in_frame(frame):
    """Enhance all Entry and Text widgets in a frame with clipboard support."""
    clipboard_helper = ClipboardMixin()
    
    def enhance_widget(widget):
        if isinstance(widget, (ttk.Entry, tk.Entry, tk.Text)):
            clipboard_helper.add_clipboard_support(widget)
        
        # Recursively check children
        for child in widget.winfo_children():
            enhance_widget(child)
    
    enhance_widget(frame)
