"""
Create a release package for TeleGroup Automator.
"""

import os
import shutil
import zipfile
from datetime import datetime
from pathlib import Path

def create_release_package():
    """Create a release package with all necessary files."""
    print("Creating release package...")
    
    # Create release directory
    release_name = f"TeleGroupAutomator_v1.0.0_{datetime.now().strftime('%Y%m%d')}"
    release_dir = Path(f"release/{release_name}")
    
    if release_dir.exists():
        shutil.rmtree(release_dir)
    
    release_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy executable and supporting files
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("✗ Error: dist directory not found. Please run build_exe.py first.")
        return False
    
    # Copy main executable
    exe_file = dist_dir / "TeleGroupAutomator.exe"
    if exe_file.exists():
        shutil.copy2(exe_file, release_dir)
        print("✓ Copied executable")
    else:
        print("✗ Error: Executable not found")
        return False
    
    # Copy README
    readme_file = dist_dir / "README.txt"
    if readme_file.exists():
        shutil.copy2(readme_file, release_dir)
        print("✓ Copied README")
    
    # Copy requirements for reference
    req_file = dist_dir / "requirements.txt"
    if req_file.exists():
        shutil.copy2(req_file, release_dir)
        print("✓ Copied requirements.txt")
    
    # Create data and sessions directories
    (release_dir / "data").mkdir(exist_ok=True)
    (release_dir / "sessions").mkdir(exist_ok=True)
    print("✓ Created data and sessions directories")
    
    # Create installation guide
    install_guide = """
# TeleGroup Automator - Installation Guide

## Quick Start
1. Extract all files to a folder of your choice
2. Run TeleGroupAutomator.exe
3. The application will create necessary data directories on first run

## First Time Setup
1. Launch the application
2. Go to the "Accounts" tab
3. Click "Add Account" and enter your Telegram API credentials
4. Follow the authentication process
5. Start creating groups in the "Create Groups" tab

## System Requirements
- Windows 10 or later (64-bit)
- Internet connection for Telegram API access
- At least 100MB free disk space

## Getting Telegram API Credentials
1. Go to https://my.telegram.org/
2. Log in with your phone number
3. Go to "API development tools"
4. Create a new application
5. Note down your API ID and API Hash

## Important Notes
- Keep your API credentials secure and private
- Respect Telegram's terms of service and rate limits
- The application stores session data locally for convenience
- Always backup your data directory before major updates

## Troubleshooting
- If the application doesn't start, try running as administrator
- Check Windows Defender/antivirus settings if the exe is blocked
- Ensure you have a stable internet connection
- Check the logs in the data directory for error details

## Support
For technical issues, check the application logs in the data/logs directory.

## Version: 1.0.0
## Build Date: """ + datetime.now().strftime('%Y-%m-%d') + """
"""
    
    with open(release_dir / "INSTALLATION_GUIDE.txt", 'w') as f:
        f.write(install_guide.strip())
    print("✓ Created installation guide")
    
    # Create license file
    license_text = """
TeleGroup Automator - License

Copyright (c) 2024 TeleGroup Automator

This software is provided "as is", without warranty of any kind, express or
implied, including but not limited to the warranties of merchantability,
fitness for a particular purpose and noninfringement.

This software is intended for educational and personal use only.

Users are responsible for:
- Complying with Telegram's Terms of Service
- Respecting rate limits and API usage guidelines
- Using the software ethically and legally
- Securing their API credentials and session data

The developers are not responsible for any misuse of this software or any
consequences arising from its use.

By using this software, you agree to these terms and conditions.
"""
    
    with open(release_dir / "LICENSE.txt", 'w') as f:
        f.write(license_text.strip())
    print("✓ Created license file")
    
    return release_dir

def create_zip_package(release_dir):
    """Create a ZIP package of the release."""
    print("Creating ZIP package...")
    
    zip_name = f"{release_dir.name}.zip"
    zip_path = release_dir.parent / zip_name
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in release_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(release_dir.parent)
                zipf.write(file_path, arcname)
    
    print(f"✓ Created ZIP package: {zip_path}")
    print(f"✓ Package size: {zip_path.stat().st_size / (1024*1024):.1f} MB")
    
    return zip_path

def verify_release(release_dir):
    """Verify the release package contents."""
    print("Verifying release package...")
    
    required_files = [
        "TeleGroupAutomator.exe",
        "README.txt",
        "INSTALLATION_GUIDE.txt",
        "LICENSE.txt",
        "requirements.txt"
    ]
    
    required_dirs = [
        "data",
        "sessions"
    ]
    
    for file_name in required_files:
        file_path = release_dir / file_name
        if file_path.exists():
            print(f"✓ {file_name} - {file_path.stat().st_size} bytes")
        else:
            print(f"✗ Missing: {file_name}")
            return False
    
    for dir_name in required_dirs:
        dir_path = release_dir / dir_name
        if dir_path.exists() and dir_path.is_dir():
            print(f"✓ {dir_name}/ directory")
        else:
            print(f"✗ Missing directory: {dir_name}")
            return False
    
    return True

def main():
    """Main release creation process."""
    print("=" * 60)
    print("TeleGroup Automator - Release Package Creator")
    print("=" * 60)
    
    try:
        # Create release package
        release_dir = create_release_package()
        if not release_dir:
            return False
        
        # Verify package
        if not verify_release(release_dir):
            print("✗ Release verification failed")
            return False
        
        # Create ZIP package
        zip_path = create_zip_package(release_dir)
        
        print("\n" + "=" * 60)
        print("🎉 Release package created successfully!")
        print(f"📁 Release directory: {release_dir}")
        print(f"📦 ZIP package: {zip_path}")
        print("\n📋 Package contents:")
        print("   • TeleGroupAutomator.exe (Main application)")
        print("   • README.txt (User documentation)")
        print("   • INSTALLATION_GUIDE.txt (Setup instructions)")
        print("   • LICENSE.txt (License terms)")
        print("   • requirements.txt (Dependencies reference)")
        print("   • data/ (Application data directory)")
        print("   • sessions/ (Telegram session files directory)")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"✗ Error creating release: {e}")
        return False

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
