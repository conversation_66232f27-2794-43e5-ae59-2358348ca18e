"""
Accounts management tab for the TeleGroup Automator GUI.
"""

import tkinter as tk
from tkinter import ttk, messagebox
try:
    from tkinter import simpledialog
except ImportError:
    import tkinter.simpledialog as simpledialog
import asyncio
import threading
from typing import Callable, Optional
import logging
from pathlib import Path

from database.models import TelegramAccount, DatabaseManager
from core.telegram_manager import TelegramMultiAccountManager
from utils.config import SESSIONS_DIR
from utils.clipboard_support import ClipboardMixin

class AccountsTab:
    """GUI tab for managing Telegram accounts."""
    
    def __init__(self, parent, db_manager: DatabaseManager, 
                 telegram_manager: TelegramMultiAccountManager,
                 status_callback: Callable[[str], None]):
        self.parent = parent
        self.db_manager = db_manager
        self.telegram_manager = telegram_manager
        self.status_callback = status_callback
        self.logger = logging.getLogger("AccountsTab")
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        self.setup_ui()
        self.refresh_accounts_list()
    
    def setup_ui(self):
        """Set up the user interface for the accounts tab."""
        # Configure grid weights
        self.frame.grid_rowconfigure(1, weight=1)
        self.frame.grid_columnconfigure(0, weight=1)
        
        # Title and controls frame
        controls_frame = ttk.Frame(self.frame)
        controls_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        controls_frame.grid_columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(controls_frame, text="Telegram Accounts Management", 
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, sticky="w", pady=(0, 10))
        
        # Add account button
        self.add_account_btn = ttk.Button(
            controls_frame, 
            text="Add New Account", 
            command=self.show_add_account_dialog
        )
        self.add_account_btn.grid(row=1, column=0, sticky="w", padx=(0, 10))
        
        # Connect all button
        self.connect_all_btn = ttk.Button(
            controls_frame,
            text="Connect All Accounts",
            command=self.connect_all_accounts
        )
        self.connect_all_btn.grid(row=1, column=1, sticky="w", padx=(0, 10))
        
        # Refresh button
        self.refresh_btn = ttk.Button(
            controls_frame,
            text="Refresh",
            command=self.refresh_accounts_list
        )
        self.refresh_btn.grid(row=1, column=2, sticky="e")
        
        # Accounts list frame
        list_frame = ttk.LabelFrame(self.frame, text="Accounts")
        list_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # Create treeview for accounts list
        columns = ("Phone Number", "Status", "Last Used")
        self.accounts_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=10)
        
        # Configure columns
        self.accounts_tree.heading("Phone Number", text="Phone Number")
        self.accounts_tree.heading("Status", text="Status")
        self.accounts_tree.heading("Last Used", text="Last Used")
        
        self.accounts_tree.column("Phone Number", width=150)
        self.accounts_tree.column("Status", width=120)
        self.accounts_tree.column("Last Used", width=150)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=scrollbar.set)
        
        # Grid the treeview and scrollbar
        self.accounts_tree.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")
        
        # Context menu for accounts
        self.context_menu = tk.Menu(self.frame, tearoff=0)
        self.context_menu.add_command(label="Connect", command=self.connect_selected_account)
        self.context_menu.add_command(label="Disconnect", command=self.disconnect_selected_account)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Remove Account", command=self.remove_selected_account)
        
        # Bind right-click to show context menu
        self.accounts_tree.bind("<Button-3>", self.show_context_menu)
        
        # Instructions frame
        instructions_frame = ttk.LabelFrame(self.frame, text="Instructions")
        instructions_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=(0, 10))
        
        instructions_text = (
            "1. Click 'Add New Account' to add a Telegram account\n"
            "2. You'll need API ID and API Hash from https://my.telegram.org\n"
            "3. Enter your phone number and 2FA password if enabled\n"
            "4. Use 'Connect All Accounts' to connect all available accounts\n"
            "5. Right-click on accounts for additional options"
        )
        
        instructions_label = ttk.Label(instructions_frame, text=instructions_text, justify="left")
        instructions_label.grid(row=0, column=0, sticky="w", padx=10, pady=10)
    
    def show_add_account_dialog(self):
        """Show dialog to add a new Telegram account."""
        self.dialog = AddAccountDialog(self.frame, self.add_account_callback)
    
    def add_account_callback(self, account_data):
        """Callback for when a new account is added."""
        try:
            # Ensure sessions directory exists
            from utils.config import SESSIONS_DIR
            Path(SESSIONS_DIR).mkdir(parents=True, exist_ok=True)

            # Create session file path
            session_file = f"{account_data['phone_number']}.session"

            # Determine status based on whether account is already authenticated
            is_authenticated = account_data.get('authenticated', False)
            status = "Active" if is_authenticated else "Login Required"

            # Create TelegramAccount object
            account = TelegramAccount(
                phone_number=account_data['phone_number'],
                api_id=account_data['api_id'],
                api_hash=account_data['api_hash'],
                session_file=session_file,
                status=status
            )

            # Add to database
            self.db_manager.add_account(account)

            # Add to telegram manager
            from core.telegram_manager import TelegramAccountManager
            manager = TelegramAccountManager(account, self.db_manager)
            self.telegram_manager.account_managers[account.phone_number] = manager

            # Refresh the list
            self.refresh_accounts_list()

            if is_authenticated:
                self.status_callback(f"Successfully added and authenticated account {account.phone_number}")
            else:
                self.status_callback(f"Added account {account.phone_number}. Click 'Connect' to authenticate.")

        except Exception as e:
            self.logger.error(f"Error adding account: {e}")
            messagebox.showerror("Error", f"Failed to add account: {e}")
    
    def refresh_accounts_list(self):
        """Refresh the accounts list display."""
        # Clear existing items
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)
        
        # Get accounts from database
        accounts = self.db_manager.get_accounts()
        
        # Add accounts to tree
        for account in accounts:
            last_used = account.last_used.strftime("%Y-%m-%d %H:%M") if account.last_used else "Never"
            
            # Color code based on status
            tags = []
            if account.status == "Active":
                tags = ["active"]
            elif account.status == "Flood Wait":
                tags = ["flood_wait"]
            elif account.status == "Login Required":
                tags = ["login_required"]
            
            self.accounts_tree.insert("", "end", values=(
                account.phone_number,
                account.status,
                last_used
            ), tags=tags)
        
        # Configure tag colors
        self.accounts_tree.tag_configure("active", foreground="green")
        self.accounts_tree.tag_configure("flood_wait", foreground="orange")
        self.accounts_tree.tag_configure("login_required", foreground="red")
    
    def show_context_menu(self, event):
        """Show context menu for selected account."""
        item = self.accounts_tree.selection()[0] if self.accounts_tree.selection() else None
        if item:
            self.context_menu.post(event.x_root, event.y_root)
    
    def connect_selected_account(self):
        """Connect the selected account."""
        selected = self.accounts_tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select an account to connect.")
            return

        item = selected[0]
        phone_number = self.accounts_tree.item(item)["values"][0]

        # Run connection in background with proper async handling
        def connect_task():
            try:
                # Create a new event loop for this thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # Run the async operation
                result = loop.run_until_complete(
                    self.telegram_manager.connect_account(phone_number)
                )

                # Schedule refresh after connection attempt
                self.frame.after(100, lambda: self.refresh_accounts_list())

                # Update status
                if result:
                    self.frame.after(100, lambda: self.status_callback(f"Successfully connected account {phone_number}"))
                else:
                    self.frame.after(100, lambda: self.status_callback(f"Failed to connect account {phone_number}"))

            except Exception as e:
                self.logger.error(f"Error connecting account {phone_number}: {e}")
                self.frame.after(100, lambda: self.status_callback(f"Error connecting account {phone_number}: {str(e)}"))
            finally:
                # Clean up the event loop
                try:
                    loop.close()
                except:
                    pass

        threading.Thread(target=connect_task, daemon=True).start()
    
    def disconnect_selected_account(self):
        """Disconnect the selected account."""
        selected = self.accounts_tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select an account to disconnect.")
            return
        
        item = selected[0]
        phone_number = self.accounts_tree.item(item)["values"][0]
        
        if phone_number in self.telegram_manager.account_managers:
            manager = self.telegram_manager.account_managers[phone_number]

            def disconnect_task():
                try:
                    # Create a new event loop for this thread
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # Run the async operation
                    loop.run_until_complete(manager.disconnect())

                    # Schedule refresh after disconnection
                    self.frame.after(100, lambda: self.refresh_accounts_list())
                    self.frame.after(100, lambda: self.status_callback(f"Disconnected account {phone_number}"))

                except Exception as e:
                    self.logger.error(f"Error disconnecting account {phone_number}: {e}")
                    self.frame.after(100, lambda: self.status_callback(f"Error disconnecting account {phone_number}: {str(e)}"))
                finally:
                    # Clean up the event loop
                    try:
                        loop.close()
                    except:
                        pass

            threading.Thread(target=disconnect_task, daemon=True).start()
    
    def remove_selected_account(self):
        """Remove the selected account."""
        selected = self.accounts_tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select an account to remove.")
            return
        
        item = selected[0]
        phone_number = self.accounts_tree.item(item)["values"][0]
        
        # Confirm removal
        if messagebox.askyesno("Confirm", f"Are you sure you want to remove account {phone_number}?"):
            try:
                # Remove from telegram manager
                if phone_number in self.telegram_manager.account_managers:
                    del self.telegram_manager.account_managers[phone_number]
                
                # Remove session file
                session_file = Path(SESSIONS_DIR) / f"{phone_number}.session"
                if session_file.exists():
                    session_file.unlink()
                
                # Remove from database (would need to implement this method)
                # self.db_manager.remove_account(phone_number)
                
                self.refresh_accounts_list()
                self.status_callback(f"Removed account {phone_number}")
                
            except Exception as e:
                self.logger.error(f"Error removing account: {e}")
                messagebox.showerror("Error", f"Failed to remove account: {e}")
    
    def connect_all_accounts(self):
        """Connect all available accounts."""
        def connect_task():
            try:
                # Create a new event loop for this thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # Run the async operation
                result = loop.run_until_complete(
                    self.telegram_manager.connect_all_accounts()
                )

                # Schedule refresh after connection attempt
                self.frame.after(100, lambda: self.refresh_accounts_list())
                self.frame.after(100, lambda: self.status_callback("Finished connecting all accounts"))

            except Exception as e:
                self.logger.error(f"Error connecting all accounts: {e}")
                self.frame.after(100, lambda: self.status_callback(f"Error connecting accounts: {str(e)}"))
            finally:
                # Clean up the event loop
                try:
                    loop.close()
                except:
                    pass

        threading.Thread(target=connect_task, daemon=True).start()

class AddAccountDialog(ClipboardMixin):
    """Dialog for adding a new Telegram account."""

    def __init__(self, parent, callback):
        super().__init__()  # Initialize ClipboardMixin
        self.callback = callback
        self.parent = parent

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Add Telegram Account")

        # Make dialog resizable for better compatibility
        self.dialog.resizable(True, True)
        self.dialog.grab_set()  # Make dialog modal

        # Set minimum size to ensure all content is visible
        self.dialog.minsize(500, 450)

        # Configure for high-DPI displays
        try:
            self.dialog.tk.call('tk', 'scaling', 1.0)
        except:
            pass  # Ignore if not supported

        # Center the dialog
        self.dialog.transient(parent)

        self.setup_ui()

        # Center and focus after UI is set up
        self.dialog.after(10, self.center_and_focus)

    def center_and_focus(self):
        """Center the dialog and set focus after UI is ready."""
        self.dialog.update_idletasks()

        # Get screen dimensions
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()

        # Calculate optimal size based on screen
        dialog_width = min(550, int(screen_width * 0.4))
        dialog_height = min(500, int(screen_height * 0.6))

        # Calculate position to center
        x = (screen_width - dialog_width) // 2
        y = (screen_height - dialog_height) // 2

        # Ensure dialog is not positioned off-screen
        x = max(0, min(x, screen_width - dialog_width))
        y = max(0, min(y, screen_height - dialog_height))

        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

        # Focus on first entry
        if hasattr(self, 'api_id_entry'):
            self.api_id_entry.focus_set()
    
    def setup_ui(self):
        """Set up the dialog UI with responsive layout."""
        # Configure dialog grid
        self.dialog.grid_rowconfigure(0, weight=1)
        self.dialog.grid_columnconfigure(0, weight=1)

        # Main container with scrollable frame for better compatibility
        canvas = tk.Canvas(self.dialog, highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.dialog, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        canvas.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        scrollbar.grid(row=0, column=1, sticky="ns", pady=20)

        # Main content frame
        main_frame = ttk.Frame(scrollable_frame)
        main_frame.pack(fill="both", expand=True)

        # Title with better spacing
        title_label = ttk.Label(main_frame, text="Add New Telegram Account",
                               font=("Segoe UI", 14, "bold"))
        title_label.pack(pady=(0, 25))

        # Form frame with better organization
        form_frame = ttk.LabelFrame(main_frame, text="Account Information", padding=20)
        form_frame.pack(fill="x", pady=(0, 20))

        # Configure form grid
        form_frame.grid_columnconfigure(1, weight=1)

        # API ID field
        ttk.Label(form_frame, text="API ID:", font=("Segoe UI", 10, "bold")).grid(
            row=0, column=0, sticky="w", pady=(0, 5))
        self.api_id_entry = ttk.Entry(form_frame, font=("Segoe UI", 10), width=30)
        self.api_id_entry.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 15))

        # API Hash field
        ttk.Label(form_frame, text="API Hash:", font=("Segoe UI", 10, "bold")).grid(
            row=2, column=0, sticky="w", pady=(0, 5))
        self.api_hash_entry = ttk.Entry(form_frame, font=("Segoe UI", 10), width=30)
        self.api_hash_entry.grid(row=3, column=0, columnspan=2, sticky="ew", pady=(0, 15))

        # Phone Number field
        ttk.Label(form_frame, text="Phone Number (with country code):", font=("Segoe UI", 10, "bold")).grid(
            row=4, column=0, sticky="w", pady=(0, 5))
        self.phone_entry = ttk.Entry(form_frame, font=("Segoe UI", 10), width=30)
        self.phone_entry.grid(row=5, column=0, columnspan=2, sticky="ew", pady=(0, 10))

        # Add placeholder text
        self.phone_entry.insert(0, "+")

        # Add clipboard support to all entry fields
        self.add_clipboard_support(self.api_id_entry)
        self.add_clipboard_support(self.api_hash_entry)
        self.add_clipboard_support(self.phone_entry)

        # Bind Enter key to add account
        self.api_id_entry.bind('<Return>', lambda e: self.add_account())
        self.api_hash_entry.bind('<Return>', lambda e: self.add_account())
        self.phone_entry.bind('<Return>', lambda e: self.add_account())
        
        # Instructions frame
        instructions_frame = ttk.LabelFrame(main_frame, text="Setup Instructions", padding=15)
        instructions_frame.pack(fill="x", pady=(0, 20))

        instructions_text = (
            "How to get your Telegram API credentials:\n\n"
            "1. Visit https://my.telegram.org and log in with your phone number\n"
            "2. Go to 'API development tools' section\n"
            "3. Create a new application (if you haven't already)\n"
            "4. Copy your API ID (numeric) and API Hash (32-character string)\n"
            "5. Enter your phone number with country code (e.g., +**********)\n\n"
            "Note: You'll be prompted for a verification code after clicking 'Add Account'"
        )

        instructions_label = ttk.Label(instructions_frame, text=instructions_text,
                                     justify="left", foreground="#666666",
                                     font=("Segoe UI", 9))
        instructions_label.pack(anchor="w")

        # Buttons frame with better styling
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(20, 10))

        # Add some spacing
        ttk.Label(button_frame, text="").pack(side="left", expand=True)

        # Cancel button
        cancel_btn = ttk.Button(button_frame, text="Cancel",
                               command=self.dialog.destroy, width=12)
        cancel_btn.pack(side="right", padx=(10, 0))

        # Add Account button (primary)
        add_btn = ttk.Button(button_frame, text="Add Account",
                            command=self.add_account, width=15)
        add_btn.pack(side="right")

        # Store button references for styling
        self.add_btn = add_btn
        self.cancel_btn = cancel_btn

        # Keyboard shortcuts
        self.dialog.bind('<Return>', lambda e: self.add_account())
        self.dialog.bind('<Escape>', lambda e: self.dialog.destroy())

        # Enable mouse wheel scrolling
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        canvas.bind("<MouseWheel>", _on_mousewheel)

        # Update canvas scroll region after everything is packed
        self.dialog.after(100, lambda: canvas.configure(scrollregion=canvas.bbox("all")))
    
    def add_account(self):
        """Add and authenticate the account with entered information."""
        api_id = self.api_id_entry.get().strip()
        api_hash = self.api_hash_entry.get().strip()
        phone = self.phone_entry.get().strip()

        # Validate inputs
        if not api_id:
            messagebox.showerror("Validation Error", "Please enter your API ID.")
            self.api_id_entry.focus_set()
            return

        if not api_hash:
            messagebox.showerror("Validation Error", "Please enter your API Hash.")
            self.api_hash_entry.focus_set()
            return

        if not phone or phone == "+":
            messagebox.showerror("Validation Error", "Please enter your phone number.")
            self.phone_entry.focus_set()
            return

        # Validate API ID is numeric
        try:
            api_id_int = int(api_id)
            if api_id_int <= 0:
                raise ValueError("API ID must be positive")
        except ValueError:
            messagebox.showerror("Validation Error", "API ID must be a positive number.")
            self.api_id_entry.focus_set()
            return

        # Validate phone number format
        if not phone.startswith("+"):
            messagebox.showerror("Validation Error", "Phone number must start with country code (+).")
            self.phone_entry.focus_set()
            return

        if len(phone) < 8:  # Minimum reasonable phone number length
            messagebox.showerror("Validation Error", "Phone number appears to be too short.")
            self.phone_entry.focus_set()
            return

        # Validate API Hash format (should be 32 characters)
        if len(api_hash) != 32:
            messagebox.showerror("Validation Error", "API Hash should be 32 characters long.")
            self.api_hash_entry.focus_set()
            return

        # Start the integrated authentication process
        self.start_authentication(api_id, api_hash, phone)

    def start_authentication(self, api_id, api_hash, phone):
        """Start the authentication process with progress indication."""
        # Disable the form during authentication
        self.api_id_entry.config(state='disabled')
        self.api_hash_entry.config(state='disabled')
        self.phone_entry.config(state='disabled')

        # Create progress frame
        progress_frame = ttk.Frame(self.dialog)
        progress_frame.pack(fill='x', padx=20, pady=10)

        self.progress_label = ttk.Label(progress_frame, text="Connecting to Telegram...")
        self.progress_label.pack()

        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill='x', pady=(5, 0))
        self.progress_bar.start()

        # Start authentication in background thread
        def auth_task():
            try:
                # Create a new event loop for this thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # Run the authentication process
                success = loop.run_until_complete(
                    self.authenticate_account(api_id, api_hash, phone)
                )

                # Schedule UI update on main thread
                self.dialog.after(100, lambda: self.authentication_completed(success))

            except Exception as e:
                # Schedule error handling on main thread
                self.dialog.after(100, lambda: self.authentication_failed(str(e)))
            finally:
                # Clean up the event loop
                try:
                    loop.close()
                except:
                    pass

        threading.Thread(target=auth_task, daemon=True).start()

    async def authenticate_account(self, api_id, api_hash, phone):
        """Perform the actual Telegram authentication."""
        from telethon import TelegramClient, errors
        from utils.config import SESSIONS_DIR
        from pathlib import Path
        import tkinter.simpledialog as simpledialog

        try:
            # Ensure sessions directory exists
            Path(SESSIONS_DIR).mkdir(parents=True, exist_ok=True)

            # Create session file path
            session_file = f"{phone}.session"
            session_path = Path(SESSIONS_DIR) / session_file

            # Update progress
            self.dialog.after(0, lambda: self.progress_label.config(text="Creating Telegram client..."))

            # Create Telegram client
            client = TelegramClient(str(session_path), int(api_id), api_hash)

            # Update progress
            self.dialog.after(0, lambda: self.progress_label.config(text="Connecting to Telegram servers..."))

            # Start the client and handle authentication
            await client.start(
                phone=phone,
                code_callback=self.get_verification_code,
                password_callback=self.get_2fa_password
            )

            # Update progress
            self.dialog.after(0, lambda: self.progress_label.config(text="Verifying authentication..."))

            # Verify we're properly authenticated
            me = await client.get_me()
            if not me:
                raise Exception("Failed to authenticate - could not retrieve user info")

            # Update progress
            self.dialog.after(0, lambda: self.progress_label.config(text="Saving account information..."))

            # Create account object
            from database.models import TelegramAccount
            account = TelegramAccount(
                phone_number=phone,
                api_id=api_id,
                api_hash=api_hash,
                session_file=session_file,
                status="Active"
            )

            # Save to database and add to manager
            self.dialog.after(0, lambda: self.save_authenticated_account(account, client))

            # Disconnect the client (we'll reconnect later when needed)
            await client.disconnect()

            return True

        except errors.FloodWaitError as e:
            self.dialog.after(0, lambda: self.progress_label.config(text=f"Rate limited - wait {e.seconds} seconds"))
            await asyncio.sleep(2)  # Show the message briefly
            return False

        except Exception as e:
            self.dialog.after(0, lambda: self.progress_label.config(text=f"Authentication failed: {str(e)}"))
            await asyncio.sleep(2)  # Show the message briefly
            return False

    def get_verification_code(self):
        """Get verification code from user via dialog."""
        # This needs to be synchronous for Telethon, so we'll use a simpler approach
        try:
            from tkinter import simpledialog
            # Update progress to inform user
            self.dialog.after(0, lambda: self.progress_label.config(text="Please enter verification code..."))

            # Use a simple input dialog
            code = simpledialog.askstring(
                "Verification Code",
                "Please enter the verification code sent to your phone:",
                parent=self.dialog
            )
            return code
        except Exception as e:
            return None

    def get_2fa_password(self):
        """Get 2FA password from user via dialog."""
        try:
            from tkinter import simpledialog
            # Update progress to inform user
            self.dialog.after(0, lambda: self.progress_label.config(text="Please enter 2FA password..."))

            password = simpledialog.askstring(
                "Two-Factor Authentication",
                "Please enter your 2FA password:",
                parent=self.dialog,
                show='*'
            )
            return password
        except Exception as e:
            return None

    def save_authenticated_account(self, account, client):
        """Save the authenticated account to database and manager."""
        try:
            # Call the original callback to save the account
            self.callback({
                "api_id": account.api_id,
                "api_hash": account.api_hash,
                "phone_number": account.phone_number,
                "authenticated": True  # Flag to indicate this is already authenticated
            })
        except Exception as e:
            self.progress_label.config(text=f"Error saving account: {str(e)}")

    def authentication_completed(self, success):
        """Handle completion of authentication process."""
        self.progress_bar.stop()

        if success:
            self.progress_label.config(text="Account successfully added and authenticated!")
            # Close dialog after a brief delay
            self.dialog.after(2000, self.dialog.destroy)
        else:
            self.progress_label.config(text="Authentication failed. Please check your credentials.")
            # Re-enable the form
            self.api_id_entry.config(state='normal')
            self.api_hash_entry.config(state='normal')
            self.phone_entry.config(state='normal')

    def authentication_failed(self, error_message):
        """Handle authentication failure."""
        self.progress_bar.stop()
        self.progress_label.config(text=f"Authentication failed: {error_message}")

        # Re-enable the form
        self.api_id_entry.config(state='normal')
        self.api_hash_entry.config(state='normal')
        self.phone_entry.config(state='normal')

        # Show error dialog
        messagebox.showerror("Authentication Failed", f"Failed to authenticate account:\n{error_message}")
