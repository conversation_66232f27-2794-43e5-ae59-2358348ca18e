"""
Accounts management tab for the TeleGroup Automator GUI.
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import asyncio
import threading
from typing import Callable, Optional
import logging
from pathlib import Path

from database.models import TelegramAccount, DatabaseManager
from core.telegram_manager import TelegramMultiAccountManager
from utils.config import SESSIONS_DIR

class AccountsTab:
    """GUI tab for managing Telegram accounts."""
    
    def __init__(self, parent, db_manager: DatabaseManager, 
                 telegram_manager: TelegramMultiAccountManager,
                 status_callback: Callable[[str], None]):
        self.parent = parent
        self.db_manager = db_manager
        self.telegram_manager = telegram_manager
        self.status_callback = status_callback
        self.logger = logging.getLogger("AccountsTab")
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        self.setup_ui()
        self.refresh_accounts_list()
    
    def setup_ui(self):
        """Set up the user interface for the accounts tab."""
        # Configure grid weights
        self.frame.grid_rowconfigure(1, weight=1)
        self.frame.grid_columnconfigure(0, weight=1)
        
        # Title and controls frame
        controls_frame = ttk.Frame(self.frame)
        controls_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        controls_frame.grid_columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(controls_frame, text="Telegram Accounts Management", 
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, sticky="w", pady=(0, 10))
        
        # Add account button
        self.add_account_btn = ttk.Button(
            controls_frame, 
            text="Add New Account", 
            command=self.show_add_account_dialog
        )
        self.add_account_btn.grid(row=1, column=0, sticky="w", padx=(0, 10))
        
        # Connect all button
        self.connect_all_btn = ttk.Button(
            controls_frame,
            text="Connect All Accounts",
            command=self.connect_all_accounts
        )
        self.connect_all_btn.grid(row=1, column=1, sticky="w", padx=(0, 10))
        
        # Refresh button
        self.refresh_btn = ttk.Button(
            controls_frame,
            text="Refresh",
            command=self.refresh_accounts_list
        )
        self.refresh_btn.grid(row=1, column=2, sticky="e")
        
        # Accounts list frame
        list_frame = ttk.LabelFrame(self.frame, text="Accounts")
        list_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # Create treeview for accounts list
        columns = ("Phone Number", "Status", "Last Used")
        self.accounts_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=10)
        
        # Configure columns
        self.accounts_tree.heading("Phone Number", text="Phone Number")
        self.accounts_tree.heading("Status", text="Status")
        self.accounts_tree.heading("Last Used", text="Last Used")
        
        self.accounts_tree.column("Phone Number", width=150)
        self.accounts_tree.column("Status", width=120)
        self.accounts_tree.column("Last Used", width=150)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=scrollbar.set)
        
        # Grid the treeview and scrollbar
        self.accounts_tree.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")
        
        # Context menu for accounts
        self.context_menu = tk.Menu(self.frame, tearoff=0)
        self.context_menu.add_command(label="Connect", command=self.connect_selected_account)
        self.context_menu.add_command(label="Disconnect", command=self.disconnect_selected_account)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Remove Account", command=self.remove_selected_account)
        
        # Bind right-click to show context menu
        self.accounts_tree.bind("<Button-3>", self.show_context_menu)
        
        # Instructions frame
        instructions_frame = ttk.LabelFrame(self.frame, text="Instructions")
        instructions_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=(0, 10))
        
        instructions_text = (
            "1. Click 'Add New Account' to add a Telegram account\n"
            "2. You'll need API ID and API Hash from https://my.telegram.org\n"
            "3. Enter your phone number and 2FA password if enabled\n"
            "4. Use 'Connect All Accounts' to connect all available accounts\n"
            "5. Right-click on accounts for additional options"
        )
        
        instructions_label = ttk.Label(instructions_frame, text=instructions_text, justify="left")
        instructions_label.grid(row=0, column=0, sticky="w", padx=10, pady=10)
    
    def show_add_account_dialog(self):
        """Show dialog to add a new Telegram account."""
        dialog = AddAccountDialog(self.frame, self.add_account_callback)
    
    def add_account_callback(self, account_data):
        """Callback for when a new account is added."""
        try:
            # Create session file path
            session_file = f"{account_data['phone_number']}.session"
            
            # Create TelegramAccount object
            account = TelegramAccount(
                phone_number=account_data['phone_number'],
                api_id=account_data['api_id'],
                api_hash=account_data['api_hash'],
                session_file=session_file,
                status="Login Required"
            )
            
            # Add to database
            self.db_manager.add_account(account)
            
            # Add to telegram manager
            from core.telegram_manager import TelegramAccountManager
            manager = TelegramAccountManager(account, self.db_manager)
            self.telegram_manager.account_managers[account.phone_number] = manager
            
            # Refresh the list
            self.refresh_accounts_list()
            
            self.status_callback(f"Added account {account.phone_number}")
            
        except Exception as e:
            self.logger.error(f"Error adding account: {e}")
            messagebox.showerror("Error", f"Failed to add account: {e}")
    
    def refresh_accounts_list(self):
        """Refresh the accounts list display."""
        # Clear existing items
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)
        
        # Get accounts from database
        accounts = self.db_manager.get_accounts()
        
        # Add accounts to tree
        for account in accounts:
            last_used = account.last_used.strftime("%Y-%m-%d %H:%M") if account.last_used else "Never"
            
            # Color code based on status
            tags = []
            if account.status == "Active":
                tags = ["active"]
            elif account.status == "Flood Wait":
                tags = ["flood_wait"]
            elif account.status == "Login Required":
                tags = ["login_required"]
            
            self.accounts_tree.insert("", "end", values=(
                account.phone_number,
                account.status,
                last_used
            ), tags=tags)
        
        # Configure tag colors
        self.accounts_tree.tag_configure("active", foreground="green")
        self.accounts_tree.tag_configure("flood_wait", foreground="orange")
        self.accounts_tree.tag_configure("login_required", foreground="red")
    
    def show_context_menu(self, event):
        """Show context menu for selected account."""
        item = self.accounts_tree.selection()[0] if self.accounts_tree.selection() else None
        if item:
            self.context_menu.post(event.x_root, event.y_root)
    
    def connect_selected_account(self):
        """Connect the selected account."""
        selected = self.accounts_tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select an account to connect.")
            return
        
        item = selected[0]
        phone_number = self.accounts_tree.item(item)["values"][0]
        
        # Run connection in background
        def connect_task():
            future = asyncio.run_coroutine_threadsafe(
                self.telegram_manager.connect_account(phone_number),
                self.telegram_manager.logger.handlers[0].loop if hasattr(self.telegram_manager.logger.handlers[0], 'loop') else None
            )
            # Schedule refresh after connection attempt
            self.frame.after(2000, self.refresh_accounts_list)
        
        threading.Thread(target=connect_task, daemon=True).start()
    
    def disconnect_selected_account(self):
        """Disconnect the selected account."""
        selected = self.accounts_tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select an account to disconnect.")
            return
        
        item = selected[0]
        phone_number = self.accounts_tree.item(item)["values"][0]
        
        if phone_number in self.telegram_manager.account_managers:
            manager = self.telegram_manager.account_managers[phone_number]
            
            def disconnect_task():
                future = asyncio.run_coroutine_threadsafe(
                    manager.disconnect(),
                    self.telegram_manager.logger.handlers[0].loop if hasattr(self.telegram_manager.logger.handlers[0], 'loop') else None
                )
                self.frame.after(1000, self.refresh_accounts_list)
            
            threading.Thread(target=disconnect_task, daemon=True).start()
    
    def remove_selected_account(self):
        """Remove the selected account."""
        selected = self.accounts_tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select an account to remove.")
            return
        
        item = selected[0]
        phone_number = self.accounts_tree.item(item)["values"][0]
        
        # Confirm removal
        if messagebox.askyesno("Confirm", f"Are you sure you want to remove account {phone_number}?"):
            try:
                # Remove from telegram manager
                if phone_number in self.telegram_manager.account_managers:
                    del self.telegram_manager.account_managers[phone_number]
                
                # Remove session file
                session_file = Path(SESSIONS_DIR) / f"{phone_number}.session"
                if session_file.exists():
                    session_file.unlink()
                
                # Remove from database (would need to implement this method)
                # self.db_manager.remove_account(phone_number)
                
                self.refresh_accounts_list()
                self.status_callback(f"Removed account {phone_number}")
                
            except Exception as e:
                self.logger.error(f"Error removing account: {e}")
                messagebox.showerror("Error", f"Failed to remove account: {e}")
    
    def connect_all_accounts(self):
        """Connect all available accounts."""
        def connect_task():
            future = asyncio.run_coroutine_threadsafe(
                self.telegram_manager.connect_all_accounts(),
                self.telegram_manager.logger.handlers[0].loop if hasattr(self.telegram_manager.logger.handlers[0], 'loop') else None
            )
            self.frame.after(3000, self.refresh_accounts_list)
        
        threading.Thread(target=connect_task, daemon=True).start()

class AddAccountDialog:
    """Dialog for adding a new Telegram account."""
    
    def __init__(self, parent, callback):
        self.callback = callback
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Add Telegram Account")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.grab_set()  # Make dialog modal
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        # Main frame
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ttk.Label(main_frame, text="Add New Telegram Account", 
                               font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Form fields
        # API ID
        ttk.Label(main_frame, text="API ID:").pack(anchor="w")
        self.api_id_entry = ttk.Entry(main_frame, width=40)
        self.api_id_entry.pack(fill="x", pady=(0, 10))
        
        # API Hash
        ttk.Label(main_frame, text="API Hash:").pack(anchor="w")
        self.api_hash_entry = ttk.Entry(main_frame, width=40)
        self.api_hash_entry.pack(fill="x", pady=(0, 10))
        
        # Phone Number
        ttk.Label(main_frame, text="Phone Number (with country code):").pack(anchor="w")
        self.phone_entry = ttk.Entry(main_frame, width=40)
        self.phone_entry.pack(fill="x", pady=(0, 10))
        
        # Instructions
        instructions = (
            "Instructions:\n"
            "1. Get API ID and Hash from https://my.telegram.org\n"
            "2. Enter phone number with country code (e.g., +1234567890)\n"
            "3. You'll be prompted for verification code after clicking Add"
        )
        
        instructions_label = ttk.Label(main_frame, text=instructions, 
                                     justify="left", foreground="gray")
        instructions_label.pack(pady=10)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(20, 0))
        
        ttk.Button(button_frame, text="Cancel", command=self.dialog.destroy).pack(side="right", padx=(10, 0))
        ttk.Button(button_frame, text="Add Account", command=self.add_account).pack(side="right")
    
    def add_account(self):
        """Add the account with entered information."""
        api_id = self.api_id_entry.get().strip()
        api_hash = self.api_hash_entry.get().strip()
        phone = self.phone_entry.get().strip()
        
        # Validate inputs
        if not all([api_id, api_hash, phone]):
            messagebox.showerror("Error", "Please fill in all fields.")
            return
        
        try:
            int(api_id)  # Validate API ID is numeric
        except ValueError:
            messagebox.showerror("Error", "API ID must be numeric.")
            return
        
        if not phone.startswith("+"):
            messagebox.showerror("Error", "Phone number must start with country code (+).")
            return
        
        # Create account data
        account_data = {
            "api_id": api_id,
            "api_hash": api_hash,
            "phone_number": phone
        }
        
        # Call callback
        self.callback(account_data)
        self.dialog.destroy()
