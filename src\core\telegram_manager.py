"""
Telegram account management and operations using Telethon.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional, Dict, Callable
from telethon import TelegramClient, errors
from telethon.tl.functions.channels import CreateChannelRequest
from telethon.tl.functions.messages import ExportChatInviteRequest

from database.models import TelegramAccount, SuperGroup, DatabaseManager
from utils.config import SESSIONS_DIR, PREDEFINED_MESSAGES, DEFAULT_MESSAGE_DELAY, DEFAULT_GROUP_CREATION_DELAY
import random

class TelegramAccountManager:
    """Manages individual Telegram account operations."""
    
    def __init__(self, account: TelegramAccount, db_manager: DatabaseManager):
        self.account = account
        self.db_manager = db_manager
        self.client: Optional[TelegramClient] = None
        self.logger = logging.getLogger(f"TelegramAccount_{account.phone_number}")
        self.is_connected = False
        
    async def connect(self) -> bool:
        """Connect to Telegram using the account credentials."""
        try:
            session_path = Path(SESSIONS_DIR) / self.account.session_file
            self.client = TelegramClient(
                str(session_path),
                int(self.account.api_id),
                self.account.api_hash
            )
            
            await self.client.start(phone=self.account.phone_number)
            self.is_connected = True
            self.logger.info(f"Successfully connected account {self.account.phone_number}")
            
            # Update account status in database
            self.db_manager.update_account_status(self.account.phone_number, "Active")
            
            return True
            
        except errors.FloodWaitError as e:
            flood_wait_until = datetime.now() + timedelta(seconds=e.seconds)
            self.logger.warning(f"Account {self.account.phone_number} flood waited for {e.seconds} seconds")
            self.db_manager.update_account_status(
                self.account.phone_number, 
                "Flood Wait", 
                flood_wait_until
            )
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to connect account {self.account.phone_number}: {e}")
            self.db_manager.update_account_status(self.account.phone_number, "Login Required")
            return False
    
    async def disconnect(self):
        """Disconnect from Telegram."""
        if self.client and self.is_connected:
            await self.client.disconnect()
            self.is_connected = False
            self.logger.info(f"Disconnected account {self.account.phone_number}")
    
    async def create_supergroup(self, group_name: str) -> Optional[SuperGroup]:
        """Create a new supergroup."""
        if not self.is_connected or not self.client:
            self.logger.error(f"Account {self.account.phone_number} not connected")
            return None
        
        try:
            # Create the supergroup
            result = await self.client(CreateChannelRequest(
                title=group_name,
                about="Created by TeleGroup Automator",
                megagroup=True  # This makes it a supergroup
            ))
            
            # Get the created channel
            channel = result.chats[0]
            
            # Generate invite link
            invite_result = await self.client(ExportChatInviteRequest(
                peer=channel,
                expire_date=None,
                usage_limit=None,
                request_needed=False
            ))
            
            # Create SuperGroup object
            supergroup = SuperGroup(
                group_id=channel.id,
                group_name=group_name,
                invite_link=invite_result.link,
                account_phone=self.account.phone_number,
                created_at=datetime.now()
            )
            
            # Save to database
            self.db_manager.add_supergroup(supergroup)
            
            self.logger.info(f"Created supergroup '{group_name}' with ID {channel.id}")
            return supergroup
            
        except errors.FloodWaitError as e:
            flood_wait_until = datetime.now() + timedelta(seconds=e.seconds)
            self.logger.warning(f"Account {self.account.phone_number} flood waited for {e.seconds} seconds")
            self.db_manager.update_account_status(
                self.account.phone_number, 
                "Flood Wait", 
                flood_wait_until
            )
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to create supergroup '{group_name}': {e}")
            return None
    
    async def send_message_to_group(self, group_id: int, message: str) -> bool:
        """Send a message to a specific group."""
        if not self.is_connected or not self.client:
            self.logger.error(f"Account {self.account.phone_number} not connected")
            return False
        
        try:
            await self.client.send_message(group_id, message)
            self.logger.info(f"Sent message to group {group_id}")
            return True
            
        except errors.FloodWaitError as e:
            flood_wait_until = datetime.now() + timedelta(seconds=e.seconds)
            self.logger.warning(f"Account {self.account.phone_number} flood waited for {e.seconds} seconds")
            self.db_manager.update_account_status(
                self.account.phone_number, 
                "Flood Wait", 
                flood_wait_until
            )
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to send message to group {group_id}: {e}")
            return False
    
    async def send_random_messages(self, group_id: int, count: int) -> int:
        """Send random messages to a group."""
        sent_count = 0
        
        for i in range(count):
            if not self.is_connected:
                break
                
            message = random.choice(PREDEFINED_MESSAGES)
            if await self.send_message_to_group(group_id, message):
                sent_count += 1
                
            # Add delay between messages
            if i < count - 1:  # Don't delay after the last message
                await asyncio.sleep(DEFAULT_MESSAGE_DELAY)
        
        return sent_count

class TelegramMultiAccountManager:
    """Manages multiple Telegram accounts and coordinates operations."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.account_managers: Dict[str, TelegramAccountManager] = {}
        self.logger = logging.getLogger("TelegramMultiAccountManager")
        self.status_callback: Optional[Callable] = None
        
    def set_status_callback(self, callback: Callable):
        """Set callback function for status updates."""
        self.status_callback = callback
    
    def _update_status(self, message: str):
        """Update status through callback if available."""
        self.logger.info(message)
        if self.status_callback:
            self.status_callback(message)
    
    async def load_accounts(self):
        """Load all accounts from database and create managers."""
        accounts = self.db_manager.get_accounts()
        
        for account in accounts:
            manager = TelegramAccountManager(account, self.db_manager)
            self.account_managers[account.phone_number] = manager
        
        self._update_status(f"Loaded {len(accounts)} accounts from database")
    
    async def connect_account(self, phone_number: str) -> bool:
        """Connect a specific account."""
        if phone_number not in self.account_managers:
            self.logger.error(f"Account {phone_number} not found")
            return False
        
        manager = self.account_managers[phone_number]
        success = await manager.connect()
        
        if success:
            self._update_status(f"Connected account {phone_number}")
        else:
            self._update_status(f"Failed to connect account {phone_number}")
        
        return success
    
    async def connect_all_accounts(self) -> List[str]:
        """Connect all available accounts. Returns list of successfully connected phone numbers."""
        connected_accounts = []
        
        for phone_number, manager in self.account_managers.items():
            # Skip accounts that are in flood wait
            if manager.account.flood_wait_until and manager.account.flood_wait_until > datetime.now():
                self._update_status(f"Skipping account {phone_number} - still in flood wait")
                continue
            
            if await manager.connect():
                connected_accounts.append(phone_number)
        
        self._update_status(f"Connected {len(connected_accounts)} accounts successfully")
        return connected_accounts
    
    async def disconnect_all_accounts(self):
        """Disconnect all connected accounts."""
        for manager in self.account_managers.values():
            await manager.disconnect()
        
        self._update_status("Disconnected all accounts")
    
    def get_available_accounts(self) -> List[TelegramAccount]:
        """Get list of accounts that are available for operations (not in flood wait)."""
        available = []
        
        for manager in self.account_managers.values():
            account = manager.account
            if (account.status == "Active" and 
                (not account.flood_wait_until or account.flood_wait_until <= datetime.now())):
                available.append(account)
        
        return available
    
    def get_connected_accounts(self) -> List[str]:
        """Get list of currently connected account phone numbers."""
        return [phone for phone, manager in self.account_managers.items() if manager.is_connected]

    async def create_supergroups_batch(self,
                                     account_phones: List[str],
                                     base_name: str,
                                     name_format: str,
                                     groups_per_account: int,
                                     send_initial_messages: bool = False,
                                     initial_message_count: int = 0) -> Dict[str, List[SuperGroup]]:
        """Create supergroups across multiple accounts concurrently."""
        results = {}

        # Create tasks for each account
        tasks = []
        for phone in account_phones:
            if phone in self.account_managers and self.account_managers[phone].is_connected:
                task = self._create_groups_for_account(
                    phone, base_name, name_format, groups_per_account,
                    send_initial_messages, initial_message_count
                )
                tasks.append((phone, task))

        # Execute tasks concurrently
        if tasks:
            self._update_status(f"Starting group creation for {len(tasks)} accounts...")

            for phone, task in tasks:
                try:
                    groups = await task
                    results[phone] = groups
                    self._update_status(f"Account {phone} created {len(groups)} groups")
                except Exception as e:
                    self.logger.error(f"Error creating groups for account {phone}: {e}")
                    results[phone] = []

        total_groups = sum(len(groups) for groups in results.values())
        self._update_status(f"Batch creation completed. Total groups created: {total_groups}")

        return results

    async def _create_groups_for_account(self,
                                       phone: str,
                                       base_name: str,
                                       name_format: str,
                                       count: int,
                                       send_initial_messages: bool,
                                       initial_message_count: int) -> List[SuperGroup]:
        """Create groups for a single account."""
        manager = self.account_managers[phone]
        created_groups = []

        for i in range(count):
            if not manager.is_connected:
                self._update_status(f"Account {phone} disconnected, stopping group creation")
                break

            # Generate group name based on format
            group_name = self._generate_group_name(base_name, name_format, i)

            # Create the group
            group = await manager.create_supergroup(group_name)

            if group:
                created_groups.append(group)
                self._update_status(f"Created group '{group_name}' for account {phone}")

                # Send initial messages if requested
                if send_initial_messages and initial_message_count > 0:
                    sent_count = await manager.send_random_messages(group.group_id, initial_message_count)
                    self.db_manager.update_group_message_count(group.group_id, sent_count)
                    self._update_status(f"Sent {sent_count} initial messages to group '{group_name}'")

                # Add delay between group creations
                if i < count - 1:
                    await asyncio.sleep(DEFAULT_GROUP_CREATION_DELAY)
            else:
                self._update_status(f"Failed to create group '{group_name}' for account {phone}")

        return created_groups

    def _generate_group_name(self, base_name: str, name_format: str, index: int = 0) -> str:
        """Generate group name based on the selected format."""
        now = datetime.now()

        if name_format == "Year Only":
            return f"{base_name}_{now.year}"
        elif name_format == "Year and Month":
            return f"{base_name}_{now.year}_{now.month:02d}"
        elif name_format == "Year, Month and Day":
            return f"{base_name}_{now.year}_{now.month:02d}_{now.day:02d}"
        else:
            # Default format with index
            return f"{base_name}_{index + 1}"

    async def send_bulk_message(self, message: str, account_phones: List[str] = None) -> Dict[str, int]:
        """Send a message to all groups across specified accounts."""
        if account_phones is None:
            account_phones = self.get_connected_accounts()

        results = {}

        for phone in account_phones:
            if phone not in self.account_managers or not self.account_managers[phone].is_connected:
                continue

            # Get all groups for this account
            groups = self.db_manager.get_supergroups(phone)
            sent_count = 0

            manager = self.account_managers[phone]

            for group in groups:
                if await manager.send_message_to_group(group.group_id, message):
                    sent_count += 1
                    # Update message count in database
                    self.db_manager.update_group_message_count(
                        group.group_id,
                        group.message_count + 1
                    )

                # Add delay between messages
                await asyncio.sleep(DEFAULT_MESSAGE_DELAY)

            results[phone] = sent_count
            self._update_status(f"Sent bulk message to {sent_count} groups for account {phone}")

        total_sent = sum(results.values())
        self._update_status(f"Bulk message sent to {total_sent} groups total")

        return results
