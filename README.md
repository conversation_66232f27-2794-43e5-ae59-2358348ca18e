# TeleGroup Automator

A professional desktop application for automating Telegram supergroup creation and management across multiple accounts.

## Features

- **Multi-Account Management**: Support for multiple Telegram accounts with session persistence
- **Automated Supergroup Creation**: Create multiple supergroups with configurable naming conventions
- **Bulk Messaging**: Send messages to all created groups across all accounts
- **Professional GUI**: Modern Tkinter interface with tabbed layout
- **Robust Error Handling**: Comprehensive handling of Telegram API limits and flood waits
- **Data Persistence**: SQLite database for storing account and group information

## Installation

1. Install Python 3.8 or higher
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

Run the application:
```bash
python main.py
```

## Building Executable

To create a standalone executable:
```bash
pip install pyinstaller
pyinstaller --onefile --windowed main.py
```

## Project Structure

```
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── src/
│   ├── gui/               # GUI components
│   ├── core/              # Core Telegram functionality
│   ├── database/          # Database models and operations
│   └── utils/             # Utility functions
├── data/                  # Application data storage
└── sessions/              # Telegram session files
```

## License

This project is for educational purposes only. Please ensure compliance with Telegram's Terms of Service.
