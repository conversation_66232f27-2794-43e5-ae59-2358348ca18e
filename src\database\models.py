"""
Database models for the TeleGroup Automator application.
"""

import sqlite3
import json
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
from dataclasses import dataclass, asdict
from utils.config import DATABASE_PATH

@dataclass
class TelegramAccount:
    """Model for Telegram account information."""
    phone_number: str
    api_id: str
    api_hash: str
    session_file: str
    status: str = "Active"  # Active, Resting, Login Required, Flood Wait
    flood_wait_until: Optional[datetime] = None
    created_at: Optional[datetime] = None
    last_used: Optional[datetime] = None
    id: Optional[int] = None

@dataclass
class SuperGroup:
    """Model for created supergroup information."""
    group_id: int
    group_name: str
    invite_link: str
    account_phone: str
    created_at: datetime
    message_count: int = 0
    last_message_sent: Optional[datetime] = None
    id: Optional[int] = None

@dataclass
class TaskExecution:
    """Model for tracking task execution history."""
    task_type: str  # "create_groups", "send_messages"
    account_phone: str
    status: str  # "running", "completed", "failed", "paused"
    started_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    id: Optional[int] = None

class DatabaseManager:
    """Database manager for handling all database operations."""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or str(DATABASE_PATH)
        self.init_database()
    
    def init_database(self):
        """Initialize the database and create tables if they don't exist."""
        # Ensure data directory exists
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Create accounts table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS accounts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    phone_number TEXT UNIQUE NOT NULL,
                    api_id TEXT NOT NULL,
                    api_hash TEXT NOT NULL,
                    session_file TEXT NOT NULL,
                    status TEXT DEFAULT 'Active',
                    flood_wait_until TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_used TIMESTAMP NULL
                )
            ''')
            
            # Create supergroups table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS supergroups (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    group_id INTEGER UNIQUE NOT NULL,
                    group_name TEXT NOT NULL,
                    invite_link TEXT NOT NULL,
                    account_phone TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    message_count INTEGER DEFAULT 0,
                    last_message_sent TIMESTAMP NULL,
                    FOREIGN KEY (account_phone) REFERENCES accounts (phone_number)
                )
            ''')
            
            # Create task_executions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS task_executions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_type TEXT NOT NULL,
                    account_phone TEXT NOT NULL,
                    status TEXT NOT NULL,
                    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP NULL,
                    error_message TEXT NULL,
                    metadata TEXT NULL,
                    FOREIGN KEY (account_phone) REFERENCES accounts (phone_number)
                )
            ''')
            
            conn.commit()
    
    def add_account(self, account: TelegramAccount) -> int:
        """Add a new Telegram account to the database."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO accounts (phone_number, api_id, api_hash, session_file, status)
                VALUES (?, ?, ?, ?, ?)
            ''', (account.phone_number, account.api_id, account.api_hash, 
                  account.session_file, account.status))
            return cursor.lastrowid
    
    def get_accounts(self) -> List[TelegramAccount]:
        """Get all Telegram accounts from the database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM accounts ORDER BY created_at')
            rows = cursor.fetchall()
            
            accounts = []
            for row in rows:
                account = TelegramAccount(
                    id=row['id'],
                    phone_number=row['phone_number'],
                    api_id=row['api_id'],
                    api_hash=row['api_hash'],
                    session_file=row['session_file'],
                    status=row['status'],
                    flood_wait_until=datetime.fromisoformat(row['flood_wait_until']) if row['flood_wait_until'] else None,
                    created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                    last_used=datetime.fromisoformat(row['last_used']) if row['last_used'] else None
                )
                accounts.append(account)
            
            return accounts
    
    def update_account_status(self, phone_number: str, status: str, flood_wait_until: datetime = None):
        """Update account status and flood wait information."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE accounts 
                SET status = ?, flood_wait_until = ?, last_used = CURRENT_TIMESTAMP
                WHERE phone_number = ?
            ''', (status, flood_wait_until.isoformat() if flood_wait_until else None, phone_number))
            conn.commit()
    
    def add_supergroup(self, group: SuperGroup) -> int:
        """Add a new supergroup to the database."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO supergroups (group_id, group_name, invite_link, account_phone, message_count)
                VALUES (?, ?, ?, ?, ?)
            ''', (group.group_id, group.group_name, group.invite_link, 
                  group.account_phone, group.message_count))
            return cursor.lastrowid
    
    def get_supergroups(self, account_phone: str = None) -> List[SuperGroup]:
        """Get supergroups, optionally filtered by account."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            if account_phone:
                cursor.execute('SELECT * FROM supergroups WHERE account_phone = ? ORDER BY created_at', (account_phone,))
            else:
                cursor.execute('SELECT * FROM supergroups ORDER BY created_at')
            
            rows = cursor.fetchall()
            
            groups = []
            for row in rows:
                group = SuperGroup(
                    id=row['id'],
                    group_id=row['group_id'],
                    group_name=row['group_name'],
                    invite_link=row['invite_link'],
                    account_phone=row['account_phone'],
                    created_at=datetime.fromisoformat(row['created_at']),
                    message_count=row['message_count'],
                    last_message_sent=datetime.fromisoformat(row['last_message_sent']) if row['last_message_sent'] else None
                )
                groups.append(group)
            
            return groups
    
    def update_group_message_count(self, group_id: int, message_count: int):
        """Update the message count for a supergroup."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE supergroups 
                SET message_count = ?, last_message_sent = CURRENT_TIMESTAMP
                WHERE group_id = ?
            ''', (message_count, group_id))
            conn.commit()
    
    def add_task_execution(self, task: TaskExecution) -> int:
        """Add a new task execution record."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            metadata_json = json.dumps(task.metadata) if task.metadata else None
            cursor.execute('''
                INSERT INTO task_executions (task_type, account_phone, status, started_at, metadata)
                VALUES (?, ?, ?, ?, ?)
            ''', (task.task_type, task.account_phone, task.status, 
                  task.started_at.isoformat(), metadata_json))
            return cursor.lastrowid
    
    def update_task_execution(self, task_id: int, status: str, error_message: str = None):
        """Update task execution status."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            completed_at = datetime.now().isoformat() if status in ['completed', 'failed'] else None
            cursor.execute('''
                UPDATE task_executions 
                SET status = ?, completed_at = ?, error_message = ?
                WHERE id = ?
            ''', (status, completed_at, error_message, task_id))
            conn.commit()
    
    def get_active_tasks(self) -> List[TaskExecution]:
        """Get all currently running tasks."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM task_executions WHERE status = "running" ORDER BY started_at')
            rows = cursor.fetchall()
            
            tasks = []
            for row in rows:
                task = TaskExecution(
                    id=row['id'],
                    task_type=row['task_type'],
                    account_phone=row['account_phone'],
                    status=row['status'],
                    started_at=datetime.fromisoformat(row['started_at']),
                    completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None,
                    error_message=row['error_message'],
                    metadata=json.loads(row['metadata']) if row['metadata'] else None
                )
                tasks.append(task)
            
            return tasks
