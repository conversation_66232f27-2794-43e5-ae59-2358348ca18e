#!/usr/bin/env python3
"""
TeleGroup Automator: Multi-Account Supergroup Creator & Messenger
Main entry point for the application.
"""

import sys
import os
import asyncio
import tkinter as tk
from tkinter import ttk
import threading
from pathlib import Path

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from gui.main_window import TeleGroupAutomatorGUI
from utils.logger import setup_logger

def main():
    """Main entry point for the application."""
    # Setup logging
    logger = setup_logger()
    logger.info("Starting TeleGroup Automator...")
    
    # Create data directory if it doesn't exist
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    # Create sessions directory if it doesn't exist
    sessions_dir = Path("sessions")
    sessions_dir.mkdir(exist_ok=True)
    
    try:
        # Create and run the GUI application
        app = TeleGroupAutomatorGUI()
        app.run()
    except Exception as e:
        logger.error(f"Application error: {e}")
        raise

if __name__ == "__main__":
    main()
