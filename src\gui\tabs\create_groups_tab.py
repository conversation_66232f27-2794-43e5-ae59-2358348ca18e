"""
Create Groups tab for the TeleGroup Automator GUI.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import asyncio
import threading
from typing import Callable, List
import logging
from datetime import datetime

from database.models import DatabaseManager
from core.telegram_manager import TelegramMultiAccountManager
from utils.clipboard_support import ClipboardMixin
from utils.config import GROUP_NAME_FORMATS

class CreateGroupsTab(ClipboardMixin):
    """GUI tab for creating supergroups."""

    def __init__(self, parent, db_manager: DatabaseManager,
                 telegram_manager: TelegramMultiAccountManager,
                 status_callback: Callable[[str], None],
                 async_runner: Callable):
        super().__init__()  # Initialize ClipboardMixin
        self.parent = parent
        self.db_manager = db_manager
        self.telegram_manager = telegram_manager
        self.status_callback = status_callback
        self.async_runner = async_runner
        self.logger = logging.getLogger("CreateGroupsTab")
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        self.setup_ui()
        
        # Task state
        self.is_running = False
    
    def setup_ui(self):
        """Set up the user interface for the create groups tab."""
        # Configure grid weights
        self.frame.grid_rowconfigure(2, weight=1)
        self.frame.grid_columnconfigure(0, weight=1)
        
        # Title
        title_label = ttk.Label(self.frame, text="Create Supergroups", 
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, sticky="w", padx=10, pady=10)
        
        # Configuration frame
        config_frame = ttk.LabelFrame(self.frame, text="Configuration")
        config_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=(0, 10))
        config_frame.grid_columnconfigure(1, weight=1)
        
        current_row = 0
        
        # Account selection
        ttk.Label(config_frame, text="Select Accounts:").grid(
            row=current_row, column=0, sticky="nw", padx=10, pady=10
        )
        
        # Account selection frame
        account_frame = ttk.Frame(config_frame)
        account_frame.grid(row=current_row, column=1, sticky="ew", padx=10, pady=10)
        
        # Scrollable frame for accounts
        self.account_canvas = tk.Canvas(account_frame, height=100)
        self.account_scrollbar = ttk.Scrollbar(account_frame, orient="vertical", 
                                              command=self.account_canvas.yview)
        self.account_scrollable_frame = ttk.Frame(self.account_canvas)
        
        self.account_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.account_canvas.configure(scrollregion=self.account_canvas.bbox("all"))
        )
        
        self.account_canvas.create_window((0, 0), window=self.account_scrollable_frame, anchor="nw")
        self.account_canvas.configure(yscrollcommand=self.account_scrollbar.set)
        
        self.account_canvas.pack(side="left", fill="both", expand=True)
        self.account_scrollbar.pack(side="right", fill="y")
        
        # Account checkboxes (will be populated dynamically)
        self.account_vars = {}
        
        current_row += 1
        
        # Number of groups per account
        ttk.Label(config_frame, text="Groups per Account:").grid(
            row=current_row, column=0, sticky="w", padx=10, pady=5
        )
        
        self.groups_count_var = tk.StringVar(value="5")
        groups_count_spinbox = ttk.Spinbox(
            config_frame, 
            from_=1, 
            to=50, 
            textvariable=self.groups_count_var,
            width=10
        )
        groups_count_spinbox.grid(row=current_row, column=1, sticky="w", padx=10, pady=5)
        
        current_row += 1
        
        # Base group name
        ttk.Label(config_frame, text="Base Group Name:").grid(
            row=current_row, column=0, sticky="w", padx=10, pady=5
        )
        
        self.base_name_var = tk.StringVar(value="My Awesome Project")
        base_name_entry = ttk.Entry(config_frame, textvariable=self.base_name_var, width=30)
        base_name_entry.grid(row=current_row, column=1, sticky="w", padx=10, pady=5)

        # Add clipboard support to base name entry
        self.add_clipboard_support(base_name_entry)
        
        current_row += 1
        
        # Group naming format
        ttk.Label(config_frame, text="Naming Format:").grid(
            row=current_row, column=0, sticky="w", padx=10, pady=5
        )
        
        self.naming_format_var = tk.StringVar(value="Year Only")
        naming_format_combo = ttk.Combobox(
            config_frame,
            textvariable=self.naming_format_var,
            values=list(GROUP_NAME_FORMATS.keys()),
            state="readonly",
            width=25
        )
        naming_format_combo.grid(row=current_row, column=1, sticky="w", padx=10, pady=5)
        
        current_row += 1
        
        # Initial messaging options
        ttk.Label(config_frame, text="Initial Messages:").grid(
            row=current_row, column=0, sticky="nw", padx=10, pady=5
        )
        
        messaging_frame = ttk.Frame(config_frame)
        messaging_frame.grid(row=current_row, column=1, sticky="w", padx=10, pady=5)
        
        self.send_messages_var = tk.BooleanVar(value=False)
        send_messages_check = ttk.Checkbutton(
            messaging_frame,
            text="Send random messages after group creation",
            variable=self.send_messages_var,
            command=self.toggle_message_count
        )
        send_messages_check.pack(anchor="w")
        
        message_count_frame = ttk.Frame(messaging_frame)
        message_count_frame.pack(anchor="w", pady=(5, 0))
        
        ttk.Label(message_count_frame, text="Number of messages:").pack(side="left")
        
        self.message_count_var = tk.StringVar(value="3")
        self.message_count_spinbox = ttk.Spinbox(
            message_count_frame,
            from_=1,
            to=20,
            textvariable=self.message_count_var,
            width=5,
            state="disabled"
        )
        self.message_count_spinbox.pack(side="left", padx=(5, 0))
        
        current_row += 1
        
        # Control buttons
        button_frame = ttk.Frame(config_frame)
        button_frame.grid(row=current_row, column=0, columnspan=2, pady=20)
        
        self.refresh_accounts_btn = ttk.Button(
            button_frame,
            text="Refresh Accounts",
            command=self.refresh_accounts
        )
        self.refresh_accounts_btn.pack(side="left", padx=(0, 10))
        
        self.start_task_btn = ttk.Button(
            button_frame,
            text="Start Task",
            command=self.start_create_groups_task,
            style="Accent.TButton"
        )
        self.start_task_btn.pack(side="left", padx=(0, 10))
        
        self.stop_task_btn = ttk.Button(
            button_frame,
            text="Stop Task",
            command=self.stop_task,
            state="disabled"
        )
        self.stop_task_btn.pack(side="left")
        
        # Progress frame
        progress_frame = ttk.LabelFrame(self.frame, text="Progress")
        progress_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=(0, 10))
        progress_frame.grid_columnconfigure(0, weight=1)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            mode="determinate"
        )
        self.progress_bar.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        
        # Progress label
        self.progress_label = ttk.Label(progress_frame, text="Ready to start")
        self.progress_label.grid(row=1, column=0, padx=10, pady=(0, 10))
        
        # Load accounts initially
        self.refresh_accounts()
    
    def toggle_message_count(self):
        """Toggle the message count spinbox based on checkbox state."""
        if self.send_messages_var.get():
            self.message_count_spinbox.config(state="normal")
        else:
            self.message_count_spinbox.config(state="disabled")
    
    def refresh_accounts(self):
        """Refresh the list of available accounts."""
        # Clear existing checkboxes
        for widget in self.account_scrollable_frame.winfo_children():
            widget.destroy()
        
        self.account_vars.clear()
        
        # Get available accounts
        accounts = self.telegram_manager.get_available_accounts()
        connected_accounts = self.telegram_manager.get_connected_accounts()
        
        if not accounts:
            ttk.Label(
                self.account_scrollable_frame,
                text="No accounts available. Please add accounts in the Accounts tab.",
                foreground="gray"
            ).pack(anchor="w", padx=5, pady=5)
            return
        
        # Create checkboxes for each account
        for account in accounts:
            var = tk.BooleanVar(value=account.phone_number in connected_accounts)
            self.account_vars[account.phone_number] = var
            
            # Determine status text and color
            status_text = ""
            text_color = "black"
            
            if account.phone_number in connected_accounts:
                status_text = " (Connected)"
                text_color = "green"
            elif account.status == "Flood Wait":
                status_text = " (Flood Wait)"
                text_color = "orange"
            elif account.status == "Login Required":
                status_text = " (Login Required)"
                text_color = "red"
            
            checkbox = ttk.Checkbutton(
                self.account_scrollable_frame,
                text=f"{account.phone_number}{status_text}",
                variable=var
            )
            checkbox.pack(anchor="w", padx=5, pady=2)
            
            # Color the text if needed (this is a bit tricky with ttk)
            if text_color != "black":
                # We'll use a label instead for colored text
                checkbox.destroy()
                frame = ttk.Frame(self.account_scrollable_frame)
                frame.pack(anchor="w", padx=5, pady=2)
                
                ttk.Checkbutton(frame, variable=var).pack(side="left")
                ttk.Label(
                    frame, 
                    text=f"{account.phone_number}{status_text}",
                    foreground=text_color
                ).pack(side="left", padx=(5, 0))
        
        self.status_callback(f"Refreshed accounts list - {len(accounts)} accounts available")
    
    def get_selected_accounts(self) -> List[str]:
        """Get list of selected account phone numbers."""
        selected = []
        for phone, var in self.account_vars.items():
            if var.get():
                selected.append(phone)
        return selected
    
    def start_create_groups_task(self):
        """Start the group creation task."""
        if self.is_running:
            messagebox.showwarning("Warning", "A task is already running.")
            return
        
        # Validate inputs
        selected_accounts = self.get_selected_accounts()
        if not selected_accounts:
            messagebox.showerror("Error", "Please select at least one account.")
            return
        
        try:
            groups_count = int(self.groups_count_var.get())
            if groups_count <= 0:
                raise ValueError("Groups count must be positive")
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number of groups.")
            return
        
        base_name = self.base_name_var.get().strip()
        if not base_name:
            messagebox.showerror("Error", "Please enter a base group name.")
            return
        
        # Get messaging options
        send_messages = self.send_messages_var.get()
        message_count = 0
        if send_messages:
            try:
                message_count = int(self.message_count_var.get())
                if message_count <= 0:
                    raise ValueError("Message count must be positive")
            except ValueError:
                messagebox.showerror("Error", "Please enter a valid number of messages.")
                return
        
        # Start the task
        self.is_running = True
        self.start_task_btn.config(state="disabled")
        self.stop_task_btn.config(state="normal")
        self.progress_var.set(0)
        self.progress_label.config(text="Starting group creation...")
        
        # Run task in background
        def run_task():
            try:
                future = self.async_runner(
                    self.telegram_manager.create_supergroups_batch(
                        selected_accounts,
                        base_name,
                        self.naming_format_var.get(),
                        groups_count,
                        send_messages,
                        message_count
                    )
                )
                
                # Monitor progress (simplified - in a real implementation you'd want better progress tracking)
                self.frame.after(1000, lambda: self.update_progress(future))
                
            except Exception as e:
                self.logger.error(f"Error starting task: {e}")
                self.frame.after(0, lambda: self.task_completed(False, str(e)))
        
        threading.Thread(target=run_task, daemon=True).start()
    
    def update_progress(self, future):
        """Update progress bar and check if task is complete."""
        if future.done():
            try:
                result = future.result()
                self.task_completed(True, result)
            except Exception as e:
                self.task_completed(False, str(e))
        else:
            # Update progress (this is simplified - you'd want actual progress tracking)
            current_progress = self.progress_var.get()
            if current_progress < 90:
                self.progress_var.set(current_progress + 10)
            
            # Check again in 1 second
            self.frame.after(1000, lambda: self.update_progress(future))
    
    def task_completed(self, success: bool, result):
        """Handle task completion."""
        self.is_running = False
        self.start_task_btn.config(state="normal")
        self.stop_task_btn.config(state="disabled")
        
        if success:
            self.progress_var.set(100)
            self.progress_label.config(text="Task completed successfully!")
            
            # Show results
            total_groups = sum(len(groups) for groups in result.values()) if isinstance(result, dict) else 0
            messagebox.showinfo("Success", f"Created {total_groups} groups successfully!")
            
        else:
            self.progress_label.config(text=f"Task failed: {result}")
            messagebox.showerror("Error", f"Task failed: {result}")
        
        # Reset progress after a delay
        self.frame.after(3000, lambda: self.progress_var.set(0))
        self.frame.after(3000, lambda: self.progress_label.config(text="Ready to start"))
    
    def stop_task(self):
        """Stop the current task."""
        if self.is_running:
            # In a real implementation, you'd want to properly cancel the async task
            self.is_running = False
            self.start_task_btn.config(state="normal")
            self.stop_task_btn.config(state="disabled")
            self.progress_label.config(text="Task stopped by user")
            self.status_callback("Group creation task stopped by user")
