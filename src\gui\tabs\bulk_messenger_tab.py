"""
Bulk Messenger tab for the TeleGroup Automator GUI.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
from typing import Callable, List
import logging

from database.models import DatabaseManager
from core.telegram_manager import TelegramMultiAccountManager

class BulkMessengerTab:
    """GUI tab for sending bulk messages to all created groups."""
    
    def __init__(self, parent, db_manager: DatabaseManager, 
                 telegram_manager: TelegramMultiAccountManager,
                 status_callback: Callable[[str], None],
                 async_runner: Callable):
        self.parent = parent
        self.db_manager = db_manager
        self.telegram_manager = telegram_manager
        self.status_callback = status_callback
        self.async_runner = async_runner
        self.logger = logging.getLogger("BulkMessengerTab")
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        self.setup_ui()
        
        # Task state
        self.is_sending = False
    
    def setup_ui(self):
        """Set up the user interface for the bulk messenger tab."""
        # Configure grid weights
        self.frame.grid_rowconfigure(1, weight=1)
        self.frame.grid_columnconfigure(0, weight=1)
        
        # Title
        title_label = ttk.Label(self.frame, text="Bulk Message Sender", 
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, sticky="w", padx=10, pady=10)
        
        # Main content frame
        content_frame = ttk.Frame(self.frame)
        content_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))
        content_frame.grid_rowconfigure(1, weight=1)
        content_frame.grid_columnconfigure(0, weight=1)
        
        # Account selection frame
        account_frame = ttk.LabelFrame(content_frame, text="Account Selection")
        account_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        account_frame.grid_columnconfigure(0, weight=1)
        
        # Account selection controls
        account_controls_frame = ttk.Frame(account_frame)
        account_controls_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        account_controls_frame.grid_columnconfigure(1, weight=1)
        
        ttk.Label(account_controls_frame, text="Send to groups from:").grid(
            row=0, column=0, sticky="w"
        )
        
        self.account_selection_var = tk.StringVar(value="all")
        
        all_accounts_radio = ttk.Radiobutton(
            account_controls_frame,
            text="All connected accounts",
            variable=self.account_selection_var,
            value="all"
        )
        all_accounts_radio.grid(row=0, column=1, sticky="w", padx=(10, 0))
        
        selected_accounts_radio = ttk.Radiobutton(
            account_controls_frame,
            text="Selected accounts only",
            variable=self.account_selection_var,
            value="selected",
            command=self.toggle_account_selection
        )
        selected_accounts_radio.grid(row=0, column=2, sticky="w", padx=(10, 0))
        
        # Account checkboxes frame (initially hidden)
        self.account_checkboxes_frame = ttk.Frame(account_frame)
        self.account_vars = {}
        
        refresh_accounts_btn = ttk.Button(
            account_controls_frame,
            text="Refresh Accounts",
            command=self.refresh_accounts
        )
        refresh_accounts_btn.grid(row=0, column=3, sticky="e", padx=(10, 0))
        
        # Message composition frame
        message_frame = ttk.LabelFrame(content_frame, text="Message Composition")
        message_frame.grid(row=1, column=0, sticky="nsew", pady=(0, 10))
        message_frame.grid_rowconfigure(1, weight=1)
        message_frame.grid_columnconfigure(0, weight=1)
        
        # Message instructions
        instructions_label = ttk.Label(
            message_frame,
            text="Compose your message below. It will be sent to all groups from the selected accounts.",
            foreground="gray"
        )
        instructions_label.grid(row=0, column=0, sticky="w", padx=10, pady=(10, 5))
        
        # Message text area
        self.message_text = scrolledtext.ScrolledText(
            message_frame,
            height=10,
            wrap=tk.WORD,
            font=("Arial", 10)
        )
        self.message_text.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))
        
        # Character counter
        self.char_count_label = ttk.Label(message_frame, text="Characters: 0")
        self.char_count_label.grid(row=2, column=0, sticky="e", padx=10, pady=(0, 10))
        
        # Bind text change event for character counter
        self.message_text.bind("<KeyRelease>", self.update_char_count)
        self.message_text.bind("<Button-1>", self.update_char_count)
        
        # Control buttons frame
        controls_frame = ttk.Frame(content_frame)
        controls_frame.grid(row=2, column=0, sticky="ew")
        
        # Statistics frame
        stats_frame = ttk.Frame(controls_frame)
        stats_frame.pack(side="left", fill="x", expand=True)
        
        self.stats_label = ttk.Label(
            stats_frame,
            text="Ready to send messages",
            foreground="gray"
        )
        self.stats_label.pack(anchor="w")
        
        # Buttons frame
        buttons_frame = ttk.Frame(controls_frame)
        buttons_frame.pack(side="right")
        
        self.preview_btn = ttk.Button(
            buttons_frame,
            text="Preview Groups",
            command=self.preview_target_groups
        )
        self.preview_btn.pack(side="left", padx=(0, 10))
        
        self.send_btn = ttk.Button(
            buttons_frame,
            text="Send Message",
            command=self.send_bulk_message,
            style="Accent.TButton"
        )
        self.send_btn.pack(side="left", padx=(0, 10))
        
        self.stop_btn = ttk.Button(
            buttons_frame,
            text="Stop Sending",
            command=self.stop_sending,
            state="disabled"
        )
        self.stop_btn.pack(side="left")
        
        # Progress frame
        progress_frame = ttk.LabelFrame(content_frame, text="Progress")
        progress_frame.grid(row=3, column=0, sticky="ew", pady=(10, 0))
        progress_frame.grid_columnconfigure(0, weight=1)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            mode="determinate"
        )
        self.progress_bar.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        
        self.progress_label = ttk.Label(progress_frame, text="Ready")
        self.progress_label.grid(row=1, column=0, padx=10, pady=(0, 10))
        
        # Load accounts initially
        self.refresh_accounts()
        self.update_stats()
    
    def toggle_account_selection(self):
        """Toggle the account selection checkboxes visibility."""
        if self.account_selection_var.get() == "selected":
            self.account_checkboxes_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=(0, 10))
        else:
            self.account_checkboxes_frame.grid_remove()
    
    def refresh_accounts(self):
        """Refresh the list of available accounts."""
        # Clear existing checkboxes
        for widget in self.account_checkboxes_frame.winfo_children():
            widget.destroy()
        
        self.account_vars.clear()
        
        # Get connected accounts
        connected_accounts = self.telegram_manager.get_connected_accounts()
        
        if not connected_accounts:
            ttk.Label(
                self.account_checkboxes_frame,
                text="No connected accounts. Please connect accounts in the Accounts tab.",
                foreground="red"
            ).pack(anchor="w", padx=5, pady=5)
            self.update_stats()
            return
        
        # Create checkboxes for each connected account
        for phone in connected_accounts:
            var = tk.BooleanVar(value=True)
            self.account_vars[phone] = var
            
            checkbox = ttk.Checkbutton(
                self.account_checkboxes_frame,
                text=phone,
                variable=var,
                command=self.update_stats
            )
            checkbox.pack(anchor="w", padx=5, pady=2)
        
        self.update_stats()
        self.status_callback(f"Refreshed accounts - {len(connected_accounts)} connected accounts found")
    
    def get_selected_accounts(self) -> List[str]:
        """Get list of selected account phone numbers."""
        if self.account_selection_var.get() == "all":
            return self.telegram_manager.get_connected_accounts()
        else:
            selected = []
            for phone, var in self.account_vars.items():
                if var.get():
                    selected.append(phone)
            return selected
    
    def update_stats(self):
        """Update the statistics display."""
        selected_accounts = self.get_selected_accounts()
        
        if not selected_accounts:
            self.stats_label.config(text="No accounts selected")
            return
        
        # Count total groups for selected accounts
        total_groups = 0
        for phone in selected_accounts:
            groups = self.db_manager.get_supergroups(phone)
            total_groups += len(groups)
        
        self.stats_label.config(
            text=f"Will send to {total_groups} groups across {len(selected_accounts)} accounts"
        )
    
    def update_char_count(self, event=None):
        """Update the character count display."""
        content = self.message_text.get("1.0", tk.END)
        char_count = len(content) - 1  # Subtract 1 for the trailing newline
        self.char_count_label.config(text=f"Characters: {char_count}")
    
    def preview_target_groups(self):
        """Show a preview of target groups."""
        selected_accounts = self.get_selected_accounts()
        
        if not selected_accounts:
            messagebox.showwarning("Warning", "No accounts selected.")
            return
        
        # Create preview window
        preview_window = tk.Toplevel(self.frame)
        preview_window.title("Target Groups Preview")
        preview_window.geometry("600x400")
        preview_window.grab_set()
        
        # Create text widget with scrollbar
        text_frame = ttk.Frame(preview_window)
        text_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        preview_text = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD, state=tk.DISABLED)
        preview_text.pack(fill="both", expand=True)
        
        # Populate with group information
        preview_text.config(state=tk.NORMAL)
        preview_text.insert("end", "Target Groups for Bulk Message:\n\n")
        
        total_groups = 0
        for phone in selected_accounts:
            groups = self.db_manager.get_supergroups(phone)
            if groups:
                preview_text.insert("end", f"Account: {phone} ({len(groups)} groups)\n")
                for group in groups:
                    preview_text.insert("end", f"  • {group.group_name}\n")
                    total_groups += 1
                preview_text.insert("end", "\n")
        
        preview_text.insert("end", f"Total: {total_groups} groups will receive the message.")
        preview_text.config(state=tk.DISABLED)
        
        # Close button
        ttk.Button(
            preview_window,
            text="Close",
            command=preview_window.destroy
        ).pack(pady=10)
    
    def send_bulk_message(self):
        """Send the bulk message to all selected groups."""
        if self.is_sending:
            messagebox.showwarning("Warning", "Already sending messages.")
            return
        
        # Validate inputs
        message = self.message_text.get("1.0", tk.END).strip()
        if not message:
            messagebox.showerror("Error", "Please enter a message to send.")
            return
        
        selected_accounts = self.get_selected_accounts()
        if not selected_accounts:
            messagebox.showerror("Error", "No accounts selected.")
            return
        
        # Confirm sending
        total_groups = sum(len(self.db_manager.get_supergroups(phone)) for phone in selected_accounts)
        
        if not messagebox.askyesno(
            "Confirm Bulk Send",
            f"Are you sure you want to send this message to {total_groups} groups across {len(selected_accounts)} accounts?"
        ):
            return
        
        # Start sending
        self.is_sending = True
        self.send_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.progress_var.set(0)
        self.progress_label.config(text="Sending messages...")
        
        def run_send_task():
            try:
                future = self.async_runner(
                    self.telegram_manager.send_bulk_message(message, selected_accounts)
                )
                
                # Monitor progress
                self.frame.after(1000, lambda: self.monitor_send_progress(future))
                
            except Exception as e:
                self.logger.error(f"Error starting bulk send: {e}")
                self.frame.after(0, lambda: self.send_completed(False, str(e)))
        
        threading.Thread(target=run_send_task, daemon=True).start()
    
    def monitor_send_progress(self, future):
        """Monitor the progress of bulk sending."""
        if future.done():
            try:
                result = future.result()
                self.send_completed(True, result)
            except Exception as e:
                self.send_completed(False, str(e))
        else:
            # Update progress (simplified)
            current_progress = self.progress_var.get()
            if current_progress < 90:
                self.progress_var.set(current_progress + 10)
            
            # Check again in 1 second
            self.frame.after(1000, lambda: self.monitor_send_progress(future))
    
    def send_completed(self, success: bool, result):
        """Handle completion of bulk sending."""
        self.is_sending = False
        self.send_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        
        if success:
            self.progress_var.set(100)
            total_sent = sum(result.values()) if isinstance(result, dict) else 0
            self.progress_label.config(text=f"Sent to {total_sent} groups successfully!")
            messagebox.showinfo("Success", f"Message sent to {total_sent} groups successfully!")
        else:
            self.progress_label.config(text=f"Sending failed: {result}")
            messagebox.showerror("Error", f"Bulk sending failed: {result}")
        
        # Reset progress after delay
        self.frame.after(3000, lambda: self.progress_var.set(0))
        self.frame.after(3000, lambda: self.progress_label.config(text="Ready"))
    
    def stop_sending(self):
        """Stop the bulk sending process."""
        if self.is_sending:
            self.is_sending = False
            self.send_btn.config(state="normal")
            self.stop_btn.config(state="disabled")
            self.progress_label.config(text="Sending stopped by user")
            self.status_callback("Bulk message sending stopped by user")
