"""
Build script for creating the TeleGroup Automator executable.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_directories():
    """Clean up previous build directories."""
    print("Cleaning up previous build directories...")

    directories_to_clean = ['build', 'dist', '__pycache__']

    for directory in directories_to_clean:
        if os.path.exists(directory):
            shutil.rmtree(directory)
            print(f"✓ Removed {directory}")

    # Clean up .pyc files
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))

    print("✓ Cleanup completed")
    return True

def create_spec_file():
    """Create a custom PyInstaller spec file."""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
    ],
    hiddenimports=[
        'telethon',
        'telethon.tl',
        'telethon.tl.types',
        'telethon.tl.functions',
        'telethon.crypto',
        'ttkthemes',
        'pyperclip',
        'sqlite3',
        'asyncio',
        'threading',
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'tkinter.filedialog',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='TeleGroupAutomator',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('TeleGroupAutomator.spec', 'w') as f:
        f.write(spec_content.strip())

    print("✓ Created PyInstaller spec file")
    return True

def build_executable():
    """Build the executable using PyInstaller."""
    print("Building executable with PyInstaller...")
    
    try:
        # Run PyInstaller with the spec file
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'TeleGroupAutomator.spec'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Executable built successfully")
            return True
        else:
            print(f"✗ PyInstaller failed with return code {result.returncode}")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Error running PyInstaller: {e}")
        return False

def create_readme():
    """Create a README file for the executable."""
    readme_content = """
# TeleGroup Automator

## Description
TeleGroup Automator is a desktop application for creating and managing Telegram supergroups across multiple accounts.

## Features
- Multi-account Telegram management
- Automated supergroup creation with configurable naming
- Bulk messaging to created groups
- Professional GUI with tabbed interface
- Comprehensive error handling and logging
- Export functionality for group links

## Usage
1. Run TeleGroupAutomator.exe
2. Add your Telegram accounts in the "Accounts" tab
3. Configure group creation settings in the "Create Groups" tab
4. Use the "Bulk Messenger" tab to send messages to all groups
5. View and manage created groups in the "Generated Groups" tab

## Requirements
- Windows 10 or later
- Internet connection for Telegram API access

## Important Notes
- Keep your API credentials secure
- Respect Telegram's rate limits and terms of service
- The application creates local database and session files

## Support
For issues or questions, please refer to the application logs in the data directory.

## Version
1.0.0

## License
This software is provided as-is for educational and personal use.
"""
    
    with open('dist/README.txt', 'w') as f:
        f.write(readme_content.strip())

    print("✓ Created README file")
    return True

def copy_additional_files():
    """Copy additional files needed for the executable."""
    print("Copying additional files...")
    
    # Create directories in dist
    os.makedirs('dist/data', exist_ok=True)
    os.makedirs('dist/sessions', exist_ok=True)
    
    # Copy requirements.txt for reference
    if os.path.exists('requirements.txt'):
        shutil.copy2('requirements.txt', 'dist/')
        print("✓ Copied requirements.txt")
    
    print("✓ Created data and sessions directories")
    return True

def verify_executable():
    """Verify that the executable was created and works."""
    exe_path = Path('dist/TeleGroupAutomator.exe')
    
    if exe_path.exists():
        print(f"✓ Executable created: {exe_path}")
        print(f"✓ File size: {exe_path.stat().st_size / (1024*1024):.1f} MB")
        
        # Try to run the executable with --help or similar to verify it works
        try:
            result = subprocess.run([str(exe_path)], timeout=5, capture_output=True)
            # If it starts without immediate crash, that's good
            print("✓ Executable appears to be working")
            return True
        except subprocess.TimeoutExpired:
            # Timeout is expected since the GUI would stay open
            print("✓ Executable started successfully (GUI opened)")
            return True
        except Exception as e:
            print(f"⚠ Warning: Could not verify executable: {e}")
            return True  # Still consider it successful
    else:
        print("✗ Executable not found")
        return False

def main():
    """Main build process."""
    print("=" * 60)
    print("TeleGroup Automator - Executable Build Script")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not os.path.exists('main.py'):
        print("✗ Error: main.py not found. Please run this script from the project root.")
        return False
    
    if not os.path.exists('src'):
        print("✗ Error: src directory not found. Please run this script from the project root.")
        return False
    
    steps = [
        ("Cleaning build directories", clean_build_directories),
        ("Creating spec file", create_spec_file),
        ("Building executable", build_executable),
        ("Copying additional files", copy_additional_files),
        ("Creating README", create_readme),
        ("Verifying executable", verify_executable),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        try:
            if not step_func():
                print(f"✗ Failed at step: {step_name}")
                return False
        except Exception as e:
            print(f"✗ Error in step '{step_name}': {e}")
            return False
    
    print("\n" + "=" * 60)
    print("🎉 Build completed successfully!")
    print("📁 Executable location: dist/TeleGroupAutomator.exe")
    print("📖 Documentation: dist/README.txt")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
