# TeleGroup Automator - Installation Guide

## Quick Start
1. Extract all files to a folder of your choice
2. Run TeleGroupAutomator.exe
3. The application will create necessary data directories on first run

## First Time Setup
1. Launch the application
2. Go to the "Accounts" tab
3. Click "Add Account" and enter your Telegram API credentials
4. Follow the authentication process
5. Start creating groups in the "Create Groups" tab

## System Requirements
- Windows 10 or later (64-bit)
- Internet connection for Telegram API access
- At least 100MB free disk space

## Getting Telegram API Credentials
1. Go to https://my.telegram.org/
2. Log in with your phone number
3. Go to "API development tools"
4. Create a new application
5. Note down your API ID and API Hash

## Important Notes
- Keep your API credentials secure and private
- Respect Telegram's terms of service and rate limits
- The application stores session data locally for convenience
- Always backup your data directory before major updates

## Troubleshooting
- If the application doesn't start, try running as administrator
- Check Windows Defender/antivirus settings if the exe is blocked
- Ensure you have a stable internet connection
- Check the logs in the data directory for error details

## Support
For technical issues, check the application logs in the data/logs directory.

## Version: 1.0.0
## Build Date: 2025-06-28