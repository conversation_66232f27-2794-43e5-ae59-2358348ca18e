#!/usr/bin/env python3
"""
Test script to simulate account addition process and identify issues.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import tkinter as tk
from tkinter import ttk
from database.models import DatabaseManager, TelegramAccount
from core.telegram_manager import TelegramMultiAccountManager
from pathlib import Path

def test_account_addition_ui():
    """Test the account addition process with a minimal UI."""
    print("=== Testing Account Addition UI Process ===")
    
    try:
        # Create database and telegram managers
        db_manager = DatabaseManager()
        telegram_manager = TelegramMultiAccountManager(db_manager)
        
        # Create a simple test window
        root = tk.Tk()
        root.title("Account Addition Test")
        root.geometry("600x400")
        
        # Status callback
        status_var = tk.StringVar()
        status_var.set("Ready to test account addition...")
        
        def status_callback(message):
            status_var.set(message)
            print(f"Status: {message}")
            root.update()
        
        # Create status label
        status_label = ttk.Label(root, textvariable=status_var)
        status_label.pack(pady=10)
        
        # Create accounts list
        accounts_frame = ttk.LabelFrame(root, text="Current Accounts")
        accounts_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        columns = ("Phone Number", "Status", "API ID")
        accounts_tree = ttk.Treeview(accounts_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            accounts_tree.heading(col, text=col)
            accounts_tree.column(col, width=150)
        
        accounts_tree.pack(fill="both", expand=True, padx=5, pady=5)
        
        def refresh_accounts():
            """Refresh the accounts display."""
            # Clear existing items
            for item in accounts_tree.get_children():
                accounts_tree.delete(item)
            
            # Get accounts from database
            accounts = db_manager.get_accounts()
            print(f"Found {len(accounts)} accounts in database")
            
            # Add accounts to tree
            for account in accounts:
                accounts_tree.insert("", "end", values=(
                    account.phone_number,
                    account.status,
                    account.api_id
                ))
            
            status_callback(f"Loaded {len(accounts)} accounts")
        
        def simulate_account_addition():
            """Simulate adding an account through the callback."""
            try:
                status_callback("Simulating account addition...")
                
                # Test account data (similar to what the dialog would send)
                account_data = {
                    'phone_number': '+**********',
                    'api_id': '12345',
                    'api_hash': 'test_hash_32_characters_long_123',
                    'authenticated': False
                }
                
                # Simulate the add_account_callback logic
                from utils.config import SESSIONS_DIR
                Path(SESSIONS_DIR).mkdir(parents=True, exist_ok=True)
                
                session_file = f"{account_data['phone_number']}.session"
                is_authenticated = account_data.get('authenticated', False)
                status = "Active" if is_authenticated else "Login Required"
                
                account = TelegramAccount(
                    phone_number=account_data['phone_number'],
                    api_id=account_data['api_id'],
                    api_hash=account_data['api_hash'],
                    session_file=session_file,
                    status=status
                )
                
                # Add to database
                account_id = db_manager.add_account(account)
                status_callback(f"Added account to database with ID: {account_id}")
                
                # Add to telegram manager
                from core.telegram_manager import TelegramAccountManager
                manager = TelegramAccountManager(account, db_manager)
                telegram_manager.account_managers[account.phone_number] = manager
                status_callback(f"Added account manager for {account.phone_number}")
                
                # Refresh the display
                refresh_accounts()
                
                status_callback(f"Successfully added account {account.phone_number}")
                
            except Exception as e:
                status_callback(f"Error adding account: {e}")
                print(f"Error details: {e}")
                import traceback
                traceback.print_exc()
        
        # Buttons
        button_frame = ttk.Frame(root)
        button_frame.pack(pady=10)
        
        refresh_btn = ttk.Button(button_frame, text="Refresh Accounts", command=refresh_accounts)
        refresh_btn.pack(side="left", padx=5)
        
        add_btn = ttk.Button(button_frame, text="Simulate Add Account", command=simulate_account_addition)
        add_btn.pack(side="left", padx=5)
        
        quit_btn = ttk.Button(button_frame, text="Quit", command=root.quit)
        quit_btn.pack(side="left", padx=5)
        
        # Initial refresh
        refresh_accounts()
        
        print("Test window created. You can:")
        print("1. Click 'Refresh Accounts' to see current accounts")
        print("2. Click 'Simulate Add Account' to test the addition process")
        print("3. Click 'Quit' to exit")
        
        # Run the test window
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"Error in UI test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("TeleGroup Automator - Account Addition Test")
    print("=" * 50)
    
    success = test_account_addition_ui()
    
    print("\n" + "=" * 50)
    if success:
        print("✓ Test completed!")
    else:
        print("✗ Test failed!")
