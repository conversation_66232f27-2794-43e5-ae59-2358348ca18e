"""
Main GUI window for the TeleGroup Automator application.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import asyncio
import threading
from typing import Optional
import logging

try:
    from ttkthemes import ThemedTk, ThemedStyle
    THEMES_AVAILABLE = True
except ImportError:
    THEMES_AVAILABLE = False

from utils.config import (
    APP_NAME, APP_VERSION, WINDOW_WIDTH, WINDOW_HEIGHT, 
    WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT, DEFAULT_THEME, AVAILABLE_THEMES
)
from utils.logger import G<PERSON><PERSON>ogHand<PERSON>
from database.models import DatabaseManager
from core.telegram_manager import TelegramMultiAccountManager
from gui.tabs.accounts_tab import AccountsTab
from gui.tabs.create_groups_tab import CreateGroupsTab
from gui.tabs.bulk_messenger_tab import BulkMessengerTab
from gui.tabs.generated_groups_tab import GeneratedGroupsTab
from gui.tabs.create_groups_tab import CreateGroupsTab
from gui.tabs.bulk_messenger_tab import Bulk<PERSON>essengerTab
from gui.tabs.generated_groups_tab import GeneratedGroupsTab

class TeleGroupAutomatorGUI:
    """Main GUI application class."""
    
    def __init__(self):
        self.root: Optional[tk.Tk] = None
        self.notebook: Optional[ttk.Notebook] = None
        self.status_bar: Optional[ttk.Label] = None
        self.log_text: Optional[scrolledtext.ScrolledText] = None
        
        # Core components
        self.db_manager = DatabaseManager()
        self.telegram_manager = TelegramMultiAccountManager(self.db_manager)
        
        # GUI components
        self.accounts_tab: Optional[AccountsTab] = None
        self.create_groups_tab: Optional[CreateGroupsTab] = None
        self.bulk_messenger_tab: Optional[BulkMessengerTab] = None
        self.generated_groups_tab: Optional[GeneratedGroupsTab] = None
        
        # Async event loop for Telegram operations
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self.loop_thread: Optional[threading.Thread] = None
        self.running_tasks = set()  # Track running tasks
        
        # Logging
        self.logger = logging.getLogger("TeleGroupAutomatorGUI")
        self.gui_log_handler: Optional[GUILogHandler] = None
        
        self.setup_gui()
        self.setup_logging()
        self.setup_async_loop()
    
    def setup_gui(self):
        """Initialize the main GUI window and components."""
        # Create main window
        if THEMES_AVAILABLE:
            self.root = ThemedTk(theme=DEFAULT_THEME)
        else:
            self.root = tk.Tk()
        
        self.root.title(f"{APP_NAME} v{APP_VERSION}")
        self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        self.root.minsize(WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT)
        
        # Configure grid weights for responsive design
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        # Create main frame
        main_frame = ttk.Frame(self.root)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=0, column=0, sticky="nsew", pady=(0, 10))
        
        # Create status and log frame
        status_log_frame = ttk.Frame(main_frame)
        status_log_frame.grid(row=1, column=0, sticky="ew")
        status_log_frame.grid_columnconfigure(0, weight=1)
        
        # Status bar
        self.status_bar = ttk.Label(
            status_log_frame, 
            text="Ready", 
            relief="sunken", 
            anchor="w"
        )
        self.status_bar.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        
        # Log area
        log_label = ttk.Label(status_log_frame, text="Activity Log:")
        log_label.grid(row=1, column=0, sticky="w")
        
        self.log_text = scrolledtext.ScrolledText(
            status_log_frame,
            height=8,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.log_text.grid(row=2, column=0, sticky="ew")
        
        # Create tabs
        self.create_tabs()

        # Set up error handling
        self.setup_error_handling()
        
        # Set up window close handler
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_tabs(self):
        """Create and add all tabs to the notebook."""
        # Accounts tab
        self.accounts_tab = AccountsTab(
            self.notebook, 
            self.db_manager, 
            self.telegram_manager,
            self.update_status
        )
        self.notebook.add(self.accounts_tab.frame, text="Accounts")
        
        # Create Groups tab
        self.create_groups_tab = CreateGroupsTab(
            self.notebook,
            self.db_manager,
            self.telegram_manager,
            self.update_status,
            self.run_async_task
        )
        self.notebook.add(self.create_groups_tab.frame, text="Create Groups")
        
        # Bulk Messenger tab
        self.bulk_messenger_tab = BulkMessengerTab(
            self.notebook,
            self.db_manager,
            self.telegram_manager,
            self.update_status,
            self.run_async_task
        )
        self.notebook.add(self.bulk_messenger_tab.frame, text="Bulk Messenger")
        
        # Generated Groups tab
        self.generated_groups_tab = GeneratedGroupsTab(
            self.notebook,
            self.db_manager,
            self.update_status
        )
        self.notebook.add(self.generated_groups_tab.frame, text="Generated Groups")

    def setup_error_handling(self):
        """Set up error handling for the application."""
        from utils.error_handler import TelegramErrorHandler

        self.error_handler = TelegramErrorHandler(self.logger)

        # Add callbacks for error handling
        self.error_handler.add_flood_wait_callback(self.handle_flood_wait)
        self.error_handler.add_error_callback(self.handle_telegram_error)

        # Set error handler in telegram manager
        if hasattr(self.telegram_manager, 'set_error_handler'):
            self.telegram_manager.set_error_handler(self.error_handler)

    def handle_flood_wait(self, phone_number: str, wait_seconds: int):
        """Handle flood wait errors."""
        self.update_status(f"Account {phone_number} hit flood wait - waiting {wait_seconds} seconds")

        # Update account status in database
        account = self.db_manager.get_account(phone_number)
        if account:
            account.status = "Flood Wait"
            account.last_error = f"Flood wait: {wait_seconds} seconds"
            self.db_manager.update_account(account)

        # Refresh accounts tab if it exists
        if hasattr(self, 'accounts_tab'):
            self.root.after(100, self.accounts_tab.refresh_accounts)

    def handle_telegram_error(self, phone_number: str, error_message: str):
        """Handle general Telegram errors."""
        self.update_status(f"Error for {phone_number}: {error_message}")

        # Update account status in database
        account = self.db_manager.get_account(phone_number)
        if account:
            account.status = "Error"
            account.last_error = error_message
            self.db_manager.update_account(account)

        # Refresh accounts tab if it exists
        if hasattr(self, 'accounts_tab'):
            self.root.after(100, self.accounts_tab.refresh_accounts)
    
    def setup_logging(self):
        """Set up GUI logging handler."""
        self.gui_log_handler = GUILogHandler(self.log_text)
        
        # Add handler to root logger
        root_logger = logging.getLogger()
        root_logger.addHandler(self.gui_log_handler)
        
        # Set telegram manager status callback
        self.telegram_manager.set_status_callback(self.update_status)
    
    def setup_async_loop(self):
        """Set up asyncio event loop in a separate thread."""
        def run_loop():
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            self.loop.run_forever()
        
        self.loop_thread = threading.Thread(target=run_loop, daemon=True)
        self.loop_thread.start()
        
        # Wait a moment for the loop to start
        import time
        time.sleep(0.1)
        
        # Load accounts asynchronously
        self.run_async_task(self.telegram_manager.load_accounts())
    
    def run_async_task(self, coro, task_name: str = None):
        """Run an async task in the background event loop."""
        if self.loop and not self.loop.is_closed():
            future = asyncio.run_coroutine_threadsafe(coro, self.loop)

            # Track the task
            if task_name:
                self.running_tasks.add(task_name)

                # Add callback to remove from tracking when done
                def task_done_callback(fut):
                    self.running_tasks.discard(task_name)
                    if fut.exception():
                        self.logger.error(f"Task {task_name} failed: {fut.exception()}")
                    else:
                        self.logger.info(f"Task {task_name} completed successfully")

                future.add_done_callback(task_done_callback)

            return future
        else:
            self.logger.error("Async loop not available")
            return None

    def cancel_all_tasks(self):
        """Cancel all running async tasks."""
        if self.loop and not self.loop.is_closed():
            # Get all tasks in the loop
            tasks = [task for task in asyncio.all_tasks(self.loop) if not task.done()]

            if tasks:
                self.logger.info(f"Cancelling {len(tasks)} running tasks")

                # Cancel all tasks
                for task in tasks:
                    task.cancel()

                # Clear tracking
                self.running_tasks.clear()

    def get_running_tasks(self) -> set:
        """Get the set of currently running task names."""
        return self.running_tasks.copy()
    
    def update_status(self, message: str):
        """Update the status bar with a message."""
        if self.status_bar:
            self.status_bar.config(text=message)
        
        # Also log the message
        self.logger.info(message)
    
    def on_closing(self):
        """Handle application closing."""
        try:
            self.logger.info("Application closing - starting cleanup")

            # Cancel all running tasks
            self.cancel_all_tasks()

            # Disconnect all Telegram accounts
            if self.telegram_manager:
                disconnect_future = self.run_async_task(
                    self.telegram_manager.disconnect_all_accounts(),
                    "disconnect_accounts"
                )

                # Wait for disconnection to complete (with timeout)
                if disconnect_future:
                    try:
                        disconnect_future.result(timeout=3.0)
                    except Exception as e:
                        self.logger.warning(f"Timeout or error during disconnect: {e}")

            # Stop the async loop
            if self.loop and not self.loop.is_closed():
                self.loop.call_soon_threadsafe(self.loop.stop)

            # Wait a moment for cleanup
            import time
            time.sleep(0.2)

            self.logger.info("Cleanup completed")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
        finally:
            self.root.destroy()
    
    def run(self):
        """Start the GUI application."""
        try:
            self.update_status("TeleGroup Automator started successfully")
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"GUI error: {e}")
            messagebox.showerror("Error", f"An error occurred: {e}")
        finally:
            # Ensure cleanup
            if self.loop and not self.loop.is_closed():
                self.loop.call_soon_threadsafe(self.loop.stop)
