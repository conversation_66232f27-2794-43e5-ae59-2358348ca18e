"""
Configuration constants and settings for the TeleGroup Automator.
"""

import os
from pathlib import Path

# Application settings
APP_NAME = "TeleGroup Automator"
APP_VERSION = "1.0.0"

# Directory paths
BASE_DIR = Path(__file__).parent.parent.parent
DATA_DIR = BASE_DIR / "data"
SESSIONS_DIR = BASE_DIR / "sessions"
LOGS_DIR = BASE_DIR / "logs"

# Database settings
DATABASE_PATH = DATA_DIR / "telegroup_automator.db"

# GUI settings
WINDOW_WIDTH = 1000
WINDOW_HEIGHT = 700
WINDOW_MIN_WIDTH = 800
WINDOW_MIN_HEIGHT = 600

# Telegram settings
DEFAULT_API_ID = ""  # Users will need to provide their own
DEFAULT_API_HASH = ""  # Users will need to provide their own

# Group naming formats
GROUP_NAME_FORMATS = {
    "Year Only": "{base_name}_{year}",
    "Year and Month": "{base_name}_{year}_{month:02d}",
    "Year, Month and Day": "{base_name}_{year}_{month:02d}_{day:02d}"
}

# Predefined messages for random messaging
PREDEFINED_MESSAGES = [
    "Hello everyone, welcome to the group!",
    "Feel free to start a discussion.",
    "Let's make this community great together.",
    "What are your thoughts on the latest news in our topic?",
    "Sharing some positive vibes today!",
    "Hope everyone is having a great day!",
    "Looking forward to interesting conversations here.",
    "This group is going to be amazing!",
    "Welcome to our community space.",
    "Let's share knowledge and learn together.",
    "Great to have everyone here!",
    "Ready for some engaging discussions?",
    "This is the beginning of something special.",
    "Excited to see what we'll accomplish together.",
    "Welcome aboard, everyone!",
    "Let's build something meaningful here.",
    "Happy to be part of this community.",
    "Looking forward to connecting with all of you.",
    "Time to make some great memories together!",
    "Welcome to our digital gathering place.",
    "Let's create something wonderful here.",
    "Glad to have you all in this group.",
    "Ready to embark on this journey together?",
    "This community is going to thrive!",
    "Welcome to the start of great conversations."
]

# Task execution settings
MAX_CONCURRENT_ACCOUNTS = 5
DEFAULT_MESSAGE_DELAY = 2  # seconds between messages
DEFAULT_GROUP_CREATION_DELAY = 3  # seconds between group creations

# Error handling settings
MAX_RETRY_ATTEMPTS = 3
RETRY_DELAY = 5  # seconds

# GUI themes
AVAILABLE_THEMES = [
    "arc",
    "equilux", 
    "adapta",
    "clam",
    "alt",
    "default"
]
DEFAULT_THEME = "arc"
