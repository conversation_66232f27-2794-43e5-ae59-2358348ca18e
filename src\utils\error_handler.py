"""
Error handling utilities for the TeleGroup Automator application.
"""

import logging
import traceback
from datetime import datetime, timedelta
from typing import Optional, Callable, Any
from functools import wraps

from telethon import errors

class TelegramErrorHandler:
    """Centralized error handling for Telegram operations."""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger("TelegramErrorHandler")
        self.flood_wait_callbacks = []
        self.error_callbacks = []
    
    def add_flood_wait_callback(self, callback: Callable[[str, int], None]):
        """Add a callback to be called when a flood wait error occurs.
        
        Args:
            callback: Function that takes (phone_number, wait_seconds) as parameters
        """
        self.flood_wait_callbacks.append(callback)
    
    def add_error_callback(self, callback: Callable[[str, str], None]):
        """Add a callback to be called when any error occurs.
        
        Args:
            callback: Function that takes (phone_number, error_message) as parameters
        """
        self.error_callbacks.append(callback)
    
    def handle_telegram_error(self, error: Exception, phone_number: str = "Unknown") -> bool:
        """Handle Telegram-specific errors.
        
        Args:
            error: The exception that occurred
            phone_number: Phone number of the account that encountered the error
            
        Returns:
            bool: True if the error was handled and operation should be retried, False otherwise
        """
        if isinstance(error, errors.FloodWaitError):
            return self._handle_flood_wait(error, phone_number)
        elif isinstance(error, errors.SessionPasswordNeededError):
            return self._handle_2fa_required(error, phone_number)
        elif isinstance(error, errors.PhoneCodeInvalidError):
            return self._handle_invalid_code(error, phone_number)
        elif isinstance(error, errors.PhoneNumberInvalidError):
            return self._handle_invalid_phone(error, phone_number)
        elif isinstance(error, errors.ApiIdInvalidError):
            return self._handle_invalid_api(error, phone_number)
        elif isinstance(error, errors.ChannelPrivateError):
            return self._handle_private_channel(error, phone_number)
        elif isinstance(error, errors.ChatAdminRequiredError):
            return self._handle_admin_required(error, phone_number)
        elif isinstance(error, errors.UserBannedInChannelError):
            return self._handle_user_banned(error, phone_number)
        else:
            return self._handle_generic_error(error, phone_number)
    
    def _handle_flood_wait(self, error: errors.FloodWaitError, phone_number: str) -> bool:
        """Handle flood wait errors."""
        wait_seconds = error.seconds
        wait_until = datetime.now() + timedelta(seconds=wait_seconds)
        
        self.logger.warning(
            f"Account {phone_number} hit flood wait limit. "
            f"Must wait {wait_seconds} seconds (until {wait_until.strftime('%H:%M:%S')})"
        )
        
        # Notify callbacks
        for callback in self.flood_wait_callbacks:
            try:
                callback(phone_number, wait_seconds)
            except Exception as e:
                self.logger.error(f"Error in flood wait callback: {e}")
        
        return False  # Don't retry immediately
    
    def _handle_2fa_required(self, error: errors.SessionPasswordNeededError, phone_number: str) -> bool:
        """Handle 2FA password required errors."""
        message = f"Account {phone_number} requires 2FA password"
        self.logger.error(message)
        
        for callback in self.error_callbacks:
            try:
                callback(phone_number, message)
            except Exception as e:
                self.logger.error(f"Error in error callback: {e}")
        
        return False
    
    def _handle_invalid_code(self, error: errors.PhoneCodeInvalidError, phone_number: str) -> bool:
        """Handle invalid verification code errors."""
        message = f"Invalid verification code for account {phone_number}"
        self.logger.error(message)
        
        for callback in self.error_callbacks:
            try:
                callback(phone_number, message)
            except Exception as e:
                self.logger.error(f"Error in error callback: {e}")
        
        return False
    
    def _handle_invalid_phone(self, error: errors.PhoneNumberInvalidError, phone_number: str) -> bool:
        """Handle invalid phone number errors."""
        message = f"Invalid phone number: {phone_number}"
        self.logger.error(message)
        
        for callback in self.error_callbacks:
            try:
                callback(phone_number, message)
            except Exception as e:
                self.logger.error(f"Error in error callback: {e}")
        
        return False
    
    def _handle_invalid_api(self, error: errors.ApiIdInvalidError, phone_number: str) -> bool:
        """Handle invalid API credentials errors."""
        message = f"Invalid API credentials for account {phone_number}"
        self.logger.error(message)
        
        for callback in self.error_callbacks:
            try:
                callback(phone_number, message)
            except Exception as e:
                self.logger.error(f"Error in error callback: {e}")
        
        return False
    
    def _handle_private_channel(self, error: errors.ChannelPrivateError, phone_number: str) -> bool:
        """Handle private channel access errors."""
        message = f"Account {phone_number} cannot access private channel"
        self.logger.warning(message)
        
        for callback in self.error_callbacks:
            try:
                callback(phone_number, message)
            except Exception as e:
                self.logger.error(f"Error in error callback: {e}")
        
        return False
    
    def _handle_admin_required(self, error: errors.ChatAdminRequiredError, phone_number: str) -> bool:
        """Handle admin privileges required errors."""
        message = f"Account {phone_number} requires admin privileges for this operation"
        self.logger.warning(message)
        
        for callback in self.error_callbacks:
            try:
                callback(phone_number, message)
            except Exception as e:
                self.logger.error(f"Error in error callback: {e}")
        
        return False
    
    def _handle_user_banned(self, error: errors.UserBannedInChannelError, phone_number: str) -> bool:
        """Handle user banned errors."""
        message = f"Account {phone_number} is banned from this channel"
        self.logger.error(message)
        
        for callback in self.error_callbacks:
            try:
                callback(phone_number, message)
            except Exception as e:
                self.logger.error(f"Error in error callback: {e}")
        
        return False
    
    def _handle_generic_error(self, error: Exception, phone_number: str) -> bool:
        """Handle generic errors."""
        error_type = type(error).__name__
        error_message = str(error)
        
        message = f"Account {phone_number} encountered {error_type}: {error_message}"
        self.logger.error(message)
        
        # Log full traceback for debugging
        self.logger.debug(f"Full traceback for {phone_number}:\n{traceback.format_exc()}")
        
        for callback in self.error_callbacks:
            try:
                callback(phone_number, message)
            except Exception as e:
                self.logger.error(f"Error in error callback: {e}")
        
        return False

def telegram_error_handler(error_handler: TelegramErrorHandler, phone_number: str = "Unknown"):
    """Decorator for handling Telegram errors in async functions."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                should_retry = error_handler.handle_telegram_error(e, phone_number)
                if should_retry:
                    # Could implement retry logic here
                    pass
                raise  # Re-raise the exception after handling
        return wrapper
    return decorator

def safe_telegram_operation(error_handler: TelegramErrorHandler, phone_number: str = "Unknown"):
    """Decorator for safely executing Telegram operations without crashing the application."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                error_handler.handle_telegram_error(e, phone_number)
                return None  # Return None instead of crashing
        return wrapper
    return decorator

class RetryManager:
    """Manages retry logic for failed operations."""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.logger = logging.getLogger("RetryManager")
    
    async def retry_operation(self, operation: Callable, *args, **kwargs) -> Any:
        """Retry an operation with exponential backoff."""
        import asyncio
        
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                if asyncio.iscoroutinefunction(operation):
                    return await operation(*args, **kwargs)
                else:
                    return operation(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if attempt < self.max_retries:
                    delay = self.base_delay * (2 ** attempt)  # Exponential backoff
                    self.logger.warning(
                        f"Operation failed (attempt {attempt + 1}/{self.max_retries + 1}). "
                        f"Retrying in {delay} seconds. Error: {e}"
                    )
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(
                        f"Operation failed after {self.max_retries + 1} attempts. "
                        f"Final error: {e}"
                    )
        
        # If we get here, all retries failed
        raise last_exception

class OperationTracker:
    """Tracks the status of ongoing operations."""
    
    def __init__(self):
        self.operations = {}
        self.logger = logging.getLogger("OperationTracker")
    
    def start_operation(self, operation_id: str, description: str):
        """Start tracking an operation."""
        self.operations[operation_id] = {
            "description": description,
            "start_time": datetime.now(),
            "status": "running"
        }
        self.logger.info(f"Started operation {operation_id}: {description}")
    
    def complete_operation(self, operation_id: str, success: bool = True, error: str = None):
        """Mark an operation as completed."""
        if operation_id in self.operations:
            operation = self.operations[operation_id]
            operation["end_time"] = datetime.now()
            operation["status"] = "completed" if success else "failed"
            operation["duration"] = operation["end_time"] - operation["start_time"]
            
            if error:
                operation["error"] = error
            
            status_msg = "completed successfully" if success else f"failed: {error}"
            self.logger.info(
                f"Operation {operation_id} {status_msg} "
                f"(duration: {operation['duration'].total_seconds():.2f}s)"
            )
    
    def get_running_operations(self) -> dict:
        """Get all currently running operations."""
        return {
            op_id: op for op_id, op in self.operations.items() 
            if op["status"] == "running"
        }
    
    def cleanup_old_operations(self, max_age_hours: int = 24):
        """Remove old completed operations from tracking."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        to_remove = []
        for op_id, operation in self.operations.items():
            if (operation["status"] != "running" and 
                operation.get("end_time", datetime.now()) < cutoff_time):
                to_remove.append(op_id)
        
        for op_id in to_remove:
            del self.operations[op_id]
        
        if to_remove:
            self.logger.info(f"Cleaned up {len(to_remove)} old operations")
